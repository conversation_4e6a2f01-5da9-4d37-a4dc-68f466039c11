<?php
/**
 * User 2FA/OTP Helper Functions
 * Handles 2FA settings and verification logic for regular users
 */

/**
 * Get user security settings from user_security_settings table
 * @param int $user_id User ID
 * @return array|null User security settings or null if not found
 */
function getUserSecuritySettings($user_id) {
    try {
        $db = getDB();
        
        $sql = "SELECT * FROM user_security_settings WHERE user_id = ?";
        $result = $db->query($sql, [$user_id]);
        
        if ($result && $result->num_rows === 1) {
            return $result->fetch_assoc();
        }
        
        // Create default security settings if they don't exist
        // Default to OTP and 2FA disabled for better user experience
        $create_sql = "INSERT INTO user_security_settings (
            user_id, otp_enabled, google_2fa_enabled, require_2fa, allow_remember_device,
            login_attempts_limit, lockout_duration, otp_expiry_minutes, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 1, 5, 30, 10, NOW(), NOW())";
        
        $db->query($create_sql, [$user_id]);
        
        // Return the newly created settings
        $result = $db->query($sql, [$user_id]);
        return $result ? $result->fetch_assoc() : null;
        
    } catch (Exception $e) {
        error_log("Error getting user security settings for user $user_id: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if OTP is enabled for a specific user
 * @param int $user_id User ID
 * @return bool True if OTP is enabled, false otherwise
 */
function isUserOTPEnabled($user_id) {
    $settings = getUserSecuritySettings($user_id);
    return $settings && $settings['otp_enabled'] == 1;
}

/**
 * Check if 2FA is required for a specific user
 * @param int $user_id User ID
 * @return bool True if 2FA is required, false otherwise
 */
function isUser2FARequired($user_id) {
    $settings = getUserSecuritySettings($user_id);
    return $settings && $settings['require_2fa'] == 1;
}

/**
 * Check if Google 2FA is enabled for a specific user
 * @param int $user_id User ID
 * @return bool True if Google 2FA is enabled, false otherwise
 */
function isUserGoogle2FAEnabled($user_id) {
    $settings = getUserSecuritySettings($user_id);
    return $settings && $settings['google_2fa_enabled'] == 1;
}

/**
 * Determine if user should go through OTP verification
 * This checks if OTP or any 2FA method is enabled
 * @param int $user_id User ID
 * @return bool True if user should verify OTP, false if they can skip
 */
function shouldUserVerifyOTP($user_id) {
    $settings = getUserSecuritySettings($user_id);

    if (!$settings) {
        // If no settings found, default to NOT requiring OTP for better UX
        // Admin can enable it per user if needed
        return false;
    }

    // User should verify OTP if:
    // 1. OTP is enabled (regardless of require_2fa setting), OR
    // 2. Google 2FA is enabled
    return ($settings['otp_enabled'] == 1) || ($settings['google_2fa_enabled'] == 1);
}

/**
 * Update user security setting
 * @param int $user_id User ID
 * @param string $setting Setting name (otp_enabled, google_2fa_enabled, require_2fa, etc.)
 * @param mixed $value New value
 * @param int $updated_by User ID of who made the change
 * @return bool True on success, false on failure
 */
function updateUserSecuritySetting($user_id, $setting, $value, $updated_by = null) {
    try {
        $db = getDB();
        
        // Validate setting name to prevent SQL injection
        $allowed_settings = [
            'otp_enabled', 'google_2fa_enabled', 'require_2fa', 'allow_remember_device',
            'login_attempts_limit', 'lockout_duration', 'otp_expiry_minutes'
        ];
        
        if (!in_array($setting, $allowed_settings)) {
            throw new Exception("Invalid setting name: $setting");
        }
        
        // Ensure user security settings exist
        getUserSecuritySettings($user_id);
        
        // Update the setting
        $sql = "UPDATE user_security_settings SET $setting = ?, updated_by = ?, updated_at = NOW() WHERE user_id = ?";
        $result = $db->query($sql, [$value, $updated_by, $user_id]);
        
        if ($result) {
            // Log the change if we have activity logging
            if (function_exists('logActivity')) {
                $action = "Security setting '$setting' changed to '$value'";
                logActivity($user_id, $action, 'user_security_settings', $user_id);
            }
            return true;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Error updating user security setting for user $user_id: " . $e->getMessage());
        return false;
    }
}

/**
 * Synchronize accounts.two_factor_enabled with user_security_settings
 * This ensures consistency between the two systems
 * @param int $user_id User ID
 * @return bool True on success, false on failure
 */
function syncUserTwoFactorSettings($user_id) {
    try {
        $db = getDB();
        
        // Get current security settings
        $settings = getUserSecuritySettings($user_id);
        if (!$settings) {
            return false;
        }
        
        // Determine if 2FA should be considered "enabled" in accounts table
        // 2FA is enabled if either OTP+require_2fa is on OR Google 2FA is on
        $two_factor_enabled = ($settings['otp_enabled'] == 1 && $settings['require_2fa'] == 1) || 
                             ($settings['google_2fa_enabled'] == 1);
        
        // Update accounts table
        $sql = "UPDATE accounts SET two_factor_enabled = ?, updated_at = NOW() WHERE id = ?";
        $result = $db->query($sql, [$two_factor_enabled ? 1 : 0, $user_id]);
        
        return $result !== false;
        
    } catch (Exception $e) {
        error_log("Error syncing user 2FA settings for user $user_id: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user 2FA setup status and recommendations
 * @param int $user_id User ID
 * @return array Status information and recommendations
 */
function getUserTwoFactorStatus($user_id) {
    $settings = getUserSecuritySettings($user_id);
    
    if (!$settings) {
        return [
            'status' => 'error',
            'message' => 'Unable to retrieve security settings',
            'recommendations' => []
        ];
    }
    
    $status = [
        'otp_enabled' => $settings['otp_enabled'] == 1,
        'google_2fa_enabled' => $settings['google_2fa_enabled'] == 1,
        'require_2fa' => $settings['require_2fa'] == 1,
        'is_protected' => shouldUserVerifyOTP($user_id),
        'recommendations' => []
    ];
    
    // Generate recommendations
    if (!$status['is_protected']) {
        $status['recommendations'][] = 'Enable two-factor authentication for better security';
    }
    
    if ($status['otp_enabled'] && !$status['require_2fa']) {
        $status['recommendations'][] = 'Consider requiring 2FA for enhanced protection';
    }
    
    if (!$status['google_2fa_enabled']) {
        $status['recommendations'][] = 'Set up Google Authenticator for more secure 2FA';
    }
    
    return $status;
}
?>
