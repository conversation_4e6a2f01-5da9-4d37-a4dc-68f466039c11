<?php
/**
 * Super Admin Google Authenticator (2FA) Functions
 * Handles 2FA setup, verification, and management for super administrators
 */

require_once __DIR__ . '/../../vendor/GoogleAuthenticator/Google2FA.php';

/**
 * Simple function to get super admin settings for 2FA
 * This is a fallback if the main settings system is not available
 */
function getSuperAdminSetting($key, $default = null) {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();

        $sql = "SELECT setting_value FROM super_admin_settings WHERE setting_key = ?";
        $result = $db->query($sql, [$key]);

        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();
            return $row['setting_value'];
        }

        return $default;
    } catch (Exception $e) {
        error_log("Error getting super admin setting '$key': " . $e->getMessage());
        return $default;
    }
}

/**
 * Get Google2FA instance
 */
function getGoogle2FA() {
    return new Google2FA();
}

/**
 * Get super admin 2FA settings
 */
function getSuperAdmin2FASettings($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "SELECT * FROM super_admin_2fa_settings WHERE super_admin_username = ?";
        $result = $db->query($sql, [$username]);
        
        if ($result && $result->num_rows === 1) {
            return $result->fetch_assoc();
        }
        
        // Create default settings if not found
        $create_sql = "INSERT INTO super_admin_2fa_settings (super_admin_username, google_2fa_enabled) VALUES (?, 0)";
        $db->query($create_sql, [$username]);
        
        // Return default settings
        return [
            'super_admin_username' => $username,
            'google_2fa_enabled' => 0,
            'google_2fa_secret' => null,
            'backup_codes' => null,
            'last_used_timestamp' => null,
            'failed_attempts' => 0,
            'locked_until' => null
        ];
    } catch (Exception $e) {
        error_log("Error getting super admin 2FA settings: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if super admin 2FA is enabled
 */
function isSuperAdmin2FAEnabled($username = 'superadmin') {
    $settings = getSuperAdmin2FASettings($username);
    return $settings && $settings['google_2fa_enabled'] == 1;
}

/**
 * Check if super admin 2FA is required (from system settings)
 */
function isSuperAdmin2FARequired() {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();

        $sql = "SELECT setting_value FROM super_admin_settings WHERE setting_key = 'require_2fa'";
        $result = $db->query($sql);

        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();
            return $row['setting_value'] == '1';
        }

        return false;
    } catch (Exception $e) {
        error_log("Error checking if super admin 2FA is required: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate new Google Authenticator secret
 */
function generateSuperAdmin2FASecret() {
    $google2fa = getGoogle2FA();
    return $google2fa->generateSecretKey();
}

/**
 * Generate QR code URL for super admin 2FA setup
 */
function generateSuperAdmin2FAQRCode($secret, $username = 'superadmin') {
    $google2fa = getGoogle2FA();
    $company = 'SecureBank Online - Super Admin';
    $holder = $username;
    
    return $google2fa->getQRCodeUrl($company, $holder, $secret);
}

/**
 * Verify Google Authenticator code
 */
function verifySuperAdmin2FACode($code, $secret, $username = 'superadmin') {
    try {
        $google2fa = getGoogle2FA();
        
        // Check if account is locked
        if (isSuperAdmin2FALocked($username)) {
            logSuperAdmin2FAAction($username, 'verification_attempt_while_locked', 'Attempted verification while account is locked');
            return false;
        }
        
        // Verify the code
        $isValid = $google2fa->verifyKey($secret, $code);
        
        if ($isValid) {
            // Reset failed attempts on successful verification
            resetSuperAdmin2FAFailedAttempts($username);
            
            // Update last used timestamp to prevent replay attacks
            updateSuperAdmin2FALastUsed($username);
            
            logSuperAdmin2FAAction($username, 'verification_success', 'Google Authenticator code verified successfully');
            return true;
        } else {
            // Increment failed attempts
            incrementSuperAdmin2FAFailedAttempts($username);
            
            logSuperAdmin2FAAction($username, 'verification_failed', 'Invalid Google Authenticator code provided');
            return false;
        }
    } catch (Exception $e) {
        error_log("Error verifying super admin 2FA code: " . $e->getMessage());
        logSuperAdmin2FAAction($username, 'verification_error', 'Error during code verification: ' . $e->getMessage());
        return false;
    }
}

/**
 * Check if super admin 2FA account is locked
 */
function isSuperAdmin2FALocked($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "SELECT locked_until FROM super_admin_2fa_settings WHERE super_admin_username = ?";
        $result = $db->query($sql, [$username]);
        
        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();
            if ($row['locked_until'] && strtotime($row['locked_until']) > time()) {
                return true;
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error checking super admin 2FA lock status: " . $e->getMessage());
        return false;
    }
}

/**
 * Increment failed 2FA attempts and lock if necessary
 */
function incrementSuperAdmin2FAFailedAttempts($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        // Get max attempts from settings
        $max_attempts = getSuperAdminSetting('super_admin_2fa_max_attempts', 5);
        $lockout_duration = getSuperAdminSetting('super_admin_2fa_lockout_duration', 30);
        
        // Increment failed attempts
        $sql = "UPDATE super_admin_2fa_settings SET failed_attempts = failed_attempts + 1 WHERE super_admin_username = ?";
        $db->query($sql, [$username]);
        
        // Check if we need to lock the account
        $check_sql = "SELECT failed_attempts FROM super_admin_2fa_settings WHERE super_admin_username = ?";
        $result = $db->query($check_sql, [$username]);
        
        if ($result && $result->num_rows === 1) {
            $row = $result->fetch_assoc();
            if ($row['failed_attempts'] >= $max_attempts) {
                // Lock the account
                $lock_until = date('Y-m-d H:i:s', time() + ($lockout_duration * 60));
                $lock_sql = "UPDATE super_admin_2fa_settings SET locked_until = ? WHERE super_admin_username = ?";
                $db->query($lock_sql, [$lock_until, $username]);
                
                logSuperAdmin2FAAction($username, 'account_locked', "Account locked for $lockout_duration minutes after $max_attempts failed attempts");
            }
        }
    } catch (Exception $e) {
        error_log("Error incrementing super admin 2FA failed attempts: " . $e->getMessage());
    }
}

/**
 * Reset failed 2FA attempts
 */
function resetSuperAdmin2FAFailedAttempts($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "UPDATE super_admin_2fa_settings SET failed_attempts = 0, locked_until = NULL WHERE super_admin_username = ?";
        $db->query($sql, [$username]);
    } catch (Exception $e) {
        error_log("Error resetting super admin 2FA failed attempts: " . $e->getMessage());
    }
}

/**
 * Update last used timestamp for replay attack prevention
 */
function updateSuperAdmin2FALastUsed($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "UPDATE super_admin_2fa_settings SET last_used_timestamp = ? WHERE super_admin_username = ?";
        $db->query($sql, [time(), $username]);
    } catch (Exception $e) {
        error_log("Error updating super admin 2FA last used timestamp: " . $e->getMessage());
    }
}

/**
 * Generate backup codes for super admin 2FA
 */
function generateSuperAdmin2FABackupCodes($count = 10) {
    $codes = [];
    for ($i = 0; $i < $count; $i++) {
        $codes[] = strtoupper(bin2hex(random_bytes(4))); // 8-character hex codes
    }
    return $codes;
}

/**
 * Save super admin 2FA settings
 */
function saveSuperAdmin2FASettings($username, $secret, $backup_codes = null) {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $backup_codes_json = $backup_codes ? json_encode($backup_codes) : null;
        
        $sql = "UPDATE super_admin_2fa_settings SET 
                google_2fa_enabled = 1, 
                google_2fa_secret = ?, 
                backup_codes = ?,
                failed_attempts = 0,
                locked_until = NULL
                WHERE super_admin_username = ?";
        
        $result = $db->query($sql, [$secret, $backup_codes_json, $username]);
        
        if ($result) {
            logSuperAdmin2FAAction($username, '2fa_enabled', 'Google Authenticator enabled for super admin');
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error saving super admin 2FA settings: " . $e->getMessage());
        return false;
    }
}

/**
 * Disable super admin 2FA
 */
function disableSuperAdmin2FA($username = 'superadmin') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "UPDATE super_admin_2fa_settings SET 
                google_2fa_enabled = 0, 
                google_2fa_secret = NULL, 
                backup_codes = NULL,
                failed_attempts = 0,
                locked_until = NULL
                WHERE super_admin_username = ?";
        
        $result = $db->query($sql, [$username]);
        
        if ($result) {
            logSuperAdmin2FAAction($username, '2fa_disabled', 'Google Authenticator disabled for super admin');
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error disabling super admin 2FA: " . $e->getMessage());
        return false;
    }
}

/**
 * Log super admin 2FA actions for audit trail
 */
function logSuperAdmin2FAAction($username, $action, $details = '') {
    try {
        require_once __DIR__ . '/../../config/database.php';
        $db = getDB();
        
        $sql = "INSERT INTO super_admin_2fa_audit (super_admin_username, action, details, ip_address, user_agent, success) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $success = strpos($action, 'success') !== false ? 1 : 0;
        
        $db->query($sql, [$username, $action, $details, $ip_address, $user_agent, $success]);
    } catch (Exception $e) {
        error_log("Error logging super admin 2FA action: " . $e->getMessage());
    }
}


