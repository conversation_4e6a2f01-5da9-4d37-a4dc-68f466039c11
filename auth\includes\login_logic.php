<?php
/**
 * User Login Logic
 * Handles authentication, validation, and security checks
 */

// Initialize variables
$error = '';
$username = '';
$page_title = 'User Login';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        redirect('admin/');
    } else {
        redirect('dashboard/');
    }
}

// Get site settings for branding
function getSiteSettings() {
    try {
        $db = getDB();
        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('site_name', 'site_logo', 'site_favicon')");

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        return [
            'site_name' => $settings['site_name'] ?? 'SecureBank Online Banking',
            'site_logo' => $settings['site_logo'] ?? '',
            'site_favicon' => $settings['site_favicon'] ?? ''
        ];
    } catch (Exception $e) {
        error_log("Error getting site settings: " . $e->getMessage());
        return [
            'site_name' => 'SecureBank Online Banking',
            'site_logo' => '',
            'site_favicon' => ''
        ];
    }
}

$site_settings = getSiteSettings();

// Determine login method based on input format
function determineLoginMethod($identifier) {
    if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
        return 'email';
    } elseif (preg_match('/^\d{10,12}$/', $identifier)) {
        return 'account_number';
    } else {
        return 'username';
    }
}

// Validate login identifier format
function validateLoginIdentifier($identifier, $method) {
    switch ($method) {
        case 'email':
            return filter_var($identifier, FILTER_VALIDATE_EMAIL) !== false;
        case 'account_number':
            return preg_match('/^\d{10,12}$/', $identifier);
        case 'username':
            return preg_match('/^[a-zA-Z0-9_]{3,30}$/', $identifier);
        default:
            return false;
    }
}

// Process login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $login_identifier = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    // Check for too many failed attempts using dynamic security settings
    $security_settings = getSecuritySettings();
    $max_attempts = $security_settings['login_attempts_limit'] ?? 5;
    $failed_attempts = getFailedLoginAttempts($login_identifier, $ip_address);

    if ($failed_attempts >= $max_attempts) {
        $lockout_duration = $security_settings['lockout_duration'] ?? 30;
        $error = "Too many failed login attempts. Please try again after {$lockout_duration} minutes.";
    } else {
        if (empty($login_identifier) || empty($password)) {
            $error = 'Please enter both login credentials and password.';
        } else {
            // Validate login identifier format
            $login_method = determineLoginMethod($login_identifier);
            if (!validateLoginIdentifier($login_identifier, $login_method)) {
                switch ($login_method) {
                    case 'email':
                        $error = 'Please enter a valid email address.';
                        break;
                    case 'account_number':
                        $error = 'Please enter a valid account number (10-12 digits).';
                        break;
                    case 'username':
                        $error = 'Please enter a valid username (3-30 characters, letters, numbers, and underscores only).';
                        break;
                    default:
                        $error = 'Please enter valid login credentials.';
                        break;
                }
            } else {
                try {
                    $db = getDB();

                    // Determine login method and build appropriate SQL query
                    $login_method = determineLoginMethod($login_identifier);
                    $sql = "SELECT id, username, password, first_name, last_name, email, account_number,
                                  balance, status, is_admin, kyc_status
                            FROM accounts
                            WHERE ";

                    switch ($login_method) {
                        case 'email':
                            $sql .= "email = ?";
                            break;
                        case 'account_number':
                            $sql .= "account_number = ?";
                            break;
                        case 'username':
                        default:
                            $sql .= "username = ?";
                            break;
                    }

                    $result = $db->query($sql, [$login_identifier]);

                    if ($result && $result->num_rows === 1) {
                        $user = $result->fetch_assoc();
                        
                        if (verifyPassword($password, $user['password'])) {
                            // Check account status
                            if ($user['status'] === 'pending') {
                                $error = 'Your account is pending admin approval. Please wait for activation.';
                            } elseif ($user['status'] === 'suspended') {
                                $error = 'Your account has been suspended. Please contact support.';
                            } elseif ($user['status'] === 'closed') {
                                $error = 'Your account has been closed. Please contact support.';
                            } elseif (!filter_var($user['email'], FILTER_VALIDATE_EMAIL)) {
                                $error = 'Your account email address is invalid. Please contact support to update your email address before logging in.';
                            } else {
                                // Include user 2FA helper functions
                                require_once 'includes/user-2fa-functions.php';

                                // Check if user should go through OTP verification
                                // This considers both OTP enabled and 2FA required settings
                                $should_verify_otp = shouldUserVerifyOTP($user['id']);

                                if ($should_verify_otp) {
                                    // Generate and send OTP
                                    $otp = generateOTP();
                                    $user_name = $user['first_name'] . ' ' . $user['last_name'];

                                    if (storeOTP($user['id'], $otp, 10)) {
                                        $emailSent = sendOTPEmail($user['email'], $otp, $user_name);

                                        if ($emailSent) {
                                            // Set OTP session variables
                                            $_SESSION['otp_user_id'] = $user['id'];
                                            $_SESSION['otp_pending'] = true;
                                            $_SESSION['otp_email'] = $user['email'];

                                            // Record successful login attempt
                                            recordLoginAttempt($login_identifier, true);

                                            // Log activity
                                            logActivity($user['id'], 'User credentials verified, OTP sent to email');

                                            // Redirect to OTP verification
                                            redirect('auth/verify-otp.php');
                                        } else {
                                            // Email failed but OTP is stored - still allow verification
                                            $_SESSION['otp_user_id'] = $user['id'];
                                            $_SESSION['otp_pending'] = true;
                                            $_SESSION['otp_email'] = $user['email'];
                                            $_SESSION['email_failed'] = true;

                                            // Record login attempt
                                            recordLoginAttempt($login_identifier, true);

                                            // Log activity
                                            logActivity($user['id'], 'User credentials verified, OTP generated (email failed)');

                                            // Redirect to OTP verification with email failure notice
                                            redirect('auth/verify-otp.php');
                                        }
                                    } else {
                                        $error = 'Failed to generate verification code. Please try again.';
                                    }
                                } else {
                                    // 2FA/OTP not required, log user in directly
                                    $_SESSION['user_id'] = $user['id'];
                                    $_SESSION['username'] = $user['username'];
                                    $_SESSION['first_name'] = $user['first_name'];
                                    $_SESSION['last_name'] = $user['last_name'];
                                    $_SESSION['email'] = $user['email'];
                                    $_SESSION['account_number'] = $user['account_number'];
                                    $_SESSION['balance'] = $user['balance'];
                                    $_SESSION['is_admin'] = (bool)$user['is_admin'];
                                    $_SESSION['kyc_status'] = $user['kyc_status'];
                                    $_SESSION['last_activity'] = time();
                                    $_SESSION['user_logged_in'] = true;
                                    $_SESSION['login_time'] = time();

                                    // Note: Do not set is_admin_session flag here
                                    // This flag should only be set when admins log in through admin portal
                                    // Setting it here would prevent regular users from accessing user dashboard

                                    // Record successful login
                                    recordLoginAttempt($login_identifier, true);

                                    // Log activity
                                    logActivity($user['id'], 'User logged in successfully (2FA/OTP not required)');

                                    // Set success message
                                    setFlashMessage('success', 'Welcome back, ' . $user['first_name'] . '!');

                                    // Redirect to user dashboard
                                    redirect('dashboard/');
                                }
                            }
                        } else {
                            $error = 'Invalid login credentials.';
                            recordLoginAttempt($login_identifier, false);
                        }
                    } else {
                        $error = 'Invalid login credentials.';
                        recordLoginAttempt($login_identifier, false);
                    }
                } catch (Exception $e) {
                    error_log("Login error: " . $e->getMessage());
                    $error = 'An error occurred during login. Please try again.';
                }
            }
        }
    }
    
    // Store username for form repopulation on error
    $username = $login_identifier;
}
?>
