<?php
/**
 * Create a test user for login testing
 */

require_once 'config/config.php';

echo "<h2>Creating Test User for Login Testing</h2>";

try {
    $db = getDB();
    
    // Test user credentials
    $username = 'testuser';
    $email = '<EMAIL>';
    $password = 'password123';
    $first_name = 'Test';
    $last_name = 'User';
    $account_number = '**********';
    $balance = 5000.00;
    
    // Hash the password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Check if user already exists
    $check_sql = "SELECT id FROM accounts WHERE username = ? OR email = ? OR account_number = ?";
    $check_result = $db->query($check_sql, [$username, $email, $account_number]);
    
    if ($check_result && $check_result->num_rows > 0) {
        echo "<p style='color: orange;'>User already exists. Updating password...</p>";
        
        // Update existing user
        $update_sql = "UPDATE accounts SET password = ?, balance = ?, status = 'active' WHERE username = ?";
        $db->query($update_sql, [$hashed_password, $balance, $username]);
        
        // Get user ID
        $user_result = $db->query("SELECT id FROM accounts WHERE username = ?", [$username]);
        $user = $user_result->fetch_assoc();
        $user_id = $user['id'];
        
        echo "<p style='color: green;'>✓ User updated successfully!</p>";
    } else {
        // Create new user
        $insert_sql = "INSERT INTO accounts (
            username, email, password, first_name, last_name, account_number, 
            balance, status, account_type, currency, kyc_status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', 'savings', 'USD', 'approved', NOW())";
        
        $user_id = $db->insert($insert_sql, [
            $username, $email, $hashed_password, $first_name, $last_name, 
            $account_number, $balance
        ]);
        
        echo "<p style='color: green;'>✓ User created successfully with ID: $user_id</p>";
    }
    
    // Create or update user security settings (OTP disabled for testing)
    $security_check_sql = "SELECT id FROM user_security_settings WHERE user_id = ?";
    $security_result = $db->query($security_check_sql, [$user_id]);
    
    if ($security_result && $security_result->num_rows > 0) {
        // Update existing security settings
        $update_security_sql = "UPDATE user_security_settings SET 
                                otp_enabled = 0, 
                                google_2fa_enabled = 0, 
                                require_2fa = 0, 
                                allow_remember_device = 1,
                                updated_at = NOW()
                                WHERE user_id = ?";
        $db->query($update_security_sql, [$user_id]);
        echo "<p style='color: green;'>✓ Security settings updated (OTP disabled)</p>";
    } else {
        // Create new security settings
        $insert_security_sql = "INSERT INTO user_security_settings (
            user_id, otp_enabled, google_2fa_enabled, require_2fa, allow_remember_device,
            login_attempts_limit, lockout_duration, otp_expiry_minutes, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 1, 5, 30, 10, NOW(), NOW())";
        
        $db->query($insert_security_sql, [$user_id]);
        echo "<p style='color: green;'>✓ Security settings created (OTP disabled)</p>";
    }
    
    echo "<h3 style='color: green;'>✓ Test User Setup Complete!</h3>";
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Test User Credentials:</h4>";
    echo "<p><strong>Username:</strong> $username</p>";
    echo "<p><strong>Email:</strong> $email</p>";
    echo "<p><strong>Password:</strong> $password</p>";
    echo "<p><strong>Account Number:</strong> $account_number</p>";
    echo "<p><strong>Balance:</strong> $" . number_format($balance, 2) . "</p>";
    echo "<p><strong>OTP:</strong> Disabled</p>";
    echo "<p><strong>2FA:</strong> Disabled</p>";
    echo "</div>";
    
    // Verify the user can be found
    $verify_sql = "SELECT a.*, uss.otp_enabled, uss.require_2fa 
                   FROM accounts a 
                   LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                   WHERE a.username = ?";
    $verify_result = $db->query($verify_sql, [$username]);
    
    if ($verify_result && $verify_result->num_rows > 0) {
        $user_data = $verify_result->fetch_assoc();
        echo "<h4>Verification:</h4>";
        echo "<p>✓ User found in database</p>";
        echo "<p>✓ Status: " . $user_data['status'] . "</p>";
        echo "<p>✓ OTP Enabled: " . ($user_data['otp_enabled'] ? 'Yes' : 'No') . "</p>";
        echo "<p>✓ 2FA Required: " . ($user_data['require_2fa'] ? 'Yes' : 'No') . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<br><a href='login.php'>Test Login Now</a>";
echo "<br><a href='admin/user-security-management.php'>View User Security Management</a>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
a { color: #007bff; text-decoration: none; margin-right: 15px; }
a:hover { text-decoration: underline; }
</style>
