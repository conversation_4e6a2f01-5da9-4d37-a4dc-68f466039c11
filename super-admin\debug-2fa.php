<?php
/**
 * Debug script for Super Admin 2FA system
 * This script helps identify issues with the 2FA verification process
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Super Admin 2FA Debug Information</h1>";

// Check if tables exist
echo "<h2>Database Tables Check</h2>";
try {
    require_once '../config/database.php';
    $db = getDB();
    
    // Check super_admin_2fa_settings table
    $result = $db->query("SHOW TABLES LIKE 'super_admin_2fa_settings'");
    if ($result && $result->num_rows > 0) {
        echo "✅ super_admin_2fa_settings table exists<br>";
        
        // Check table structure
        $structure = $db->query("DESCRIBE super_admin_2fa_settings");
        echo "<strong>Table structure:</strong><br>";
        while ($row = $structure->fetch_assoc()) {
            echo "- {$row['Field']} ({$row['Type']})<br>";
        }
    } else {
        echo "❌ super_admin_2fa_settings table does not exist<br>";
        
        // Create the table
        $create_sql = "CREATE TABLE IF NOT EXISTS `super_admin_2fa_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `super_admin_username` varchar(100) NOT NULL,
            `google_2fa_enabled` tinyint(1) DEFAULT 0,
            `google_2fa_secret` varchar(255) DEFAULT NULL,
            `backup_codes` text DEFAULT NULL,
            `last_used_timestamp` int(11) DEFAULT NULL,
            `failed_attempts` int(11) DEFAULT 0,
            `locked_until` datetime DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `super_admin_username` (`super_admin_username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($db->query($create_sql)) {
            echo "✅ Created super_admin_2fa_settings table<br>";
        } else {
            echo "❌ Failed to create super_admin_2fa_settings table<br>";
        }
    }
    
    // Check super_admin_settings table
    $result = $db->query("SHOW TABLES LIKE 'super_admin_settings'");
    if ($result && $result->num_rows > 0) {
        echo "✅ super_admin_settings table exists<br>";
    } else {
        echo "❌ super_admin_settings table does not exist<br>";
        
        // Create the table
        $create_sql = "CREATE TABLE IF NOT EXISTS `super_admin_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(255) NOT NULL,
            `setting_value` text DEFAULT NULL,
            `setting_type` varchar(50) DEFAULT 'string',
            `setting_description` text DEFAULT NULL,
            `is_encrypted` tinyint(1) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($db->query($create_sql)) {
            echo "✅ Created super_admin_settings table<br>";
        } else {
            echo "❌ Failed to create super_admin_settings table<br>";
        }
    }
    
    // Check super_admin_2fa_audit table
    $result = $db->query("SHOW TABLES LIKE 'super_admin_2fa_audit'");
    if ($result && $result->num_rows > 0) {
        echo "✅ super_admin_2fa_audit table exists<br>";
    } else {
        echo "❌ super_admin_2fa_audit table does not exist<br>";
        
        // Create the table
        $create_sql = "CREATE TABLE IF NOT EXISTS `super_admin_2fa_audit` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `super_admin_username` varchar(100) NOT NULL,
            `action` varchar(100) NOT NULL,
            `details` text DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `success` tinyint(1) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_username` (`super_admin_username`),
            KEY `idx_action` (`action`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($db->query($create_sql)) {
            echo "✅ Created super_admin_2fa_audit table<br>";
        } else {
            echo "❌ Failed to create super_admin_2fa_audit table<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Check session state
echo "<h2>Session Information</h2>";
echo "Session ID: " . session_id() . "<br>";
echo "Super admin logged in: " . (isset($_SESSION['super_admin_logged_in']) ? 'Yes' : 'No') . "<br>";
echo "Super admin username: " . ($_SESSION['super_admin_username'] ?? 'Not set') . "<br>";
echo "2FA pending: " . (isset($_SESSION['super_admin_2fa_pending']) ? 'Yes' : 'No') . "<br>";
echo "2FA verified: " . (isset($_SESSION['super_admin_2fa_verified']) ? 'Yes' : 'No') . "<br>";

// Check 2FA settings
echo "<h2>2FA Settings Check</h2>";
$username = $_SESSION['super_admin_username'] ?? 'superadmin';
echo "Checking for username: $username<br>";

try {
    $settings = getSuperAdmin2FASettings($username);
    if ($settings) {
        echo "✅ 2FA settings found<br>";
        echo "- 2FA Enabled: " . ($settings['google_2fa_enabled'] ? 'Yes' : 'No') . "<br>";
        echo "- Secret exists: " . ($settings['google_2fa_secret'] ? 'Yes' : 'No') . "<br>";
        echo "- Failed attempts: " . ($settings['failed_attempts'] ?? 0) . "<br>";
        echo "- Locked until: " . ($settings['locked_until'] ?? 'Not locked') . "<br>";
    } else {
        echo "❌ No 2FA settings found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error getting 2FA settings: " . $e->getMessage() . "<br>";
}

// Check if 2FA is required
echo "<h2>2FA Requirements Check</h2>";
try {
    $required = isSuperAdmin2FARequired();
    echo "2FA Required (system-wide): " . ($required ? 'Yes' : 'No') . "<br>";
    
    $enabled = isSuperAdmin2FAEnabled($username);
    echo "2FA Enabled for user: " . ($enabled ? 'Yes' : 'No') . "<br>";
    
    $needs2fa = requiresSuperAdmin2FA();
    echo "User needs 2FA: " . ($needs2fa ? 'Yes' : 'No') . "<br>";
} catch (Exception $e) {
    echo "❌ Error checking 2FA requirements: " . $e->getMessage() . "<br>";
}

// Test Google2FA class
echo "<h2>Google2FA Class Test</h2>";
try {
    $google2fa = getGoogle2FA();
    echo "✅ Google2FA class loaded successfully<br>";
    
    $secret = $google2fa->generateSecretKey();
    echo "✅ Secret generation works: " . substr($secret, 0, 4) . "...<br>";
    
    $testCode = $google2fa->getCurrentOtp($secret);
    echo "✅ OTP generation works: " . $testCode . "<br>";
} catch (Exception $e) {
    echo "❌ Google2FA error: " . $e->getMessage() . "<br>";
}

echo "<h2>Recommendations</h2>";
echo "1. Make sure all database tables are created<br>";
echo "2. Check if 2FA is properly enabled for the super admin user<br>";
echo "3. Verify that the Google2FA class is working correctly<br>";
echo "4. Check error logs for any PHP errors during 2FA verification<br>";

echo "<br><a href='login.php'>Back to Login</a>";
?>
