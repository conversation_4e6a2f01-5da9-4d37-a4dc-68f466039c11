<?php
/**
 * Debug script to test redirect functionality
 */

require_once 'config/config.php';

echo "<h2>Debug Redirect Test</h2>";

// Test 1: Check if sessions are working
session_start();
$_SESSION['test'] = 'session_working';

echo "<h3>Test 1: Session Test</h3>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session Test Value: " . ($_SESSION['test'] ?? 'NOT SET') . "</p>";

// Test 2: Check URL function
echo "<h3>Test 2: URL Function Test</h3>";
echo "<p>url('dashboard/'): " . url('dashboard/') . "</p>";
echo "<p>BASE_URL: " . BASE_URL . "</p>";

// Test 3: Test redirect function (commented out to avoid actual redirect)
echo "<h3>Test 3: Redirect Function Test</h3>";
echo "<p>This would redirect to: " . url('dashboard/') . "</p>";

// Test 4: Check if headers are already sent
echo "<h3>Test 4: Headers Test</h3>";
if (headers_sent($file, $line)) {
    echo "<p style='color: red;'>Headers already sent in $file on line $line</p>";
} else {
    echo "<p style='color: green;'>Headers not sent yet - redirect should work</p>";
}

// Test 5: Manual redirect test (uncomment to test)
echo "<h3>Test 5: Manual Redirect Test</h3>";
echo "<p><a href='#' onclick='testRedirect()'>Click to test redirect</a></p>";

?>

<script>
function testRedirect() {
    if (confirm('Test redirect to dashboard?')) {
        window.location.href = '<?php echo url('dashboard/'); ?>';
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
</style>
