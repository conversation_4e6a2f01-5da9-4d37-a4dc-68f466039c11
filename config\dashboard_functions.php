<?php
/**
 * Dashboard Database Functions
 * Reusable functions for common dashboard database queries
 */

/**
 * Get user account information with balance
 */
function getUserAccountInfo($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT id, account_number, username, email, first_name, last_name, phone, address,
                       date_of_birth, occupation, marital_status, gender, currency, account_type,
                       balance, status, kyc_status, created_at, last_login, two_factor_enabled
                FROM accounts WHERE id = ?";
        $result = $db->query($sql, [$user_id]);
        return $result->fetch_assoc();
    } catch (Exception $e) {
        error_log("Error getting user account info: " . $e->getMessage());
        return null;
    }
}

/**
 * Get user's recent transactions
 */
function getUserRecentTransactions($user_id, $limit = 10) {
    try {
        $db = getDB();
        $sql = "SELECT t.*,
                       CASE
                           WHEN t.sender_id = ? THEN 'sent'
                           ELSE 'received'
                       END as direction,
                       CASE
                           WHEN t.sender_id = ? THEN t.recipient_name
                           ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                       END as other_party
                FROM transfers t
                WHERE (t.sender_id = ? OR t.recipient_id = ?)
                AND t.status = 'completed'
                ORDER BY t.created_at DESC
                LIMIT ?";
        
        $result = $db->query($sql, [$user_id, $user_id, $user_id, $user_id, $limit]);
        $transactions = [];
        while ($row = $result->fetch_assoc()) {
            $transactions[] = $row;
        }
        return $transactions;
    } catch (Exception $e) {
        error_log("Error getting recent transactions: " . $e->getMessage());
        return [];
    }
}

/**
 * Get monthly transaction statistics
 */
function getMonthlyTransactionStats($user_id, $month = null) {
    try {
        $db = getDB();
        $current_month = $month ?: date('Y-m');
        
        $sql = "SELECT 
                    SUM(CASE WHEN sender_id != ? AND status = 'completed' THEN amount ELSE 0 END) as money_in,
                    SUM(CASE WHEN sender_id = ? AND status = 'completed' THEN amount ELSE 0 END) as money_out,
                    COUNT(CASE WHEN status = 'completed' AND DATE_FORMAT(created_at, '%Y-%m') = ? THEN 1 END) as total_transactions
                FROM transfers 
                WHERE (sender_id = ? OR recipient_id = ?) 
                AND DATE_FORMAT(created_at, '%Y-%m') = ?";
        
        $result = $db->query($sql, [$user_id, $user_id, $current_month, $user_id, $user_id, $current_month]);
        $stats = $result->fetch_assoc();
        
        return [
            'money_in' => $stats['money_in'] ?? 0,
            'money_out' => $stats['money_out'] ?? 0,
            'total_transactions' => $stats['total_transactions'] ?? 0,
            'net_flow' => ($stats['money_in'] ?? 0) - ($stats['money_out'] ?? 0)
        ];
    } catch (Exception $e) {
        error_log("Error getting monthly stats: " . $e->getMessage());
        return ['money_in' => 0, 'money_out' => 0, 'total_transactions' => 0, 'net_flow' => 0];
    }
}

/**
 * Get user's virtual cards
 */
function getUserVirtualCards($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT vc.*,
                       COUNT(vct.id) as transaction_count,
                       SUM(CASE WHEN vct.transaction_type = 'debit' THEN vct.amount ELSE 0 END) as total_spent,
                       MAX(vct.created_at) as last_transaction_date
                FROM virtual_cards vc
                LEFT JOIN virtual_card_transactions vct ON vc.id = vct.card_id
                WHERE vc.user_id = ?
                GROUP BY vc.id
                ORDER BY vc.created_at DESC";
        
        $result = $db->query($sql, [$user_id]);
        $cards = [];
        while ($row = $result->fetch_assoc()) {
            $cards[] = $row;
        }
        return $cards;
    } catch (Exception $e) {
        error_log("Error getting virtual cards: " . $e->getMessage());
        return [];
    }
}

/**
 * Get virtual card statistics
 */
function getVirtualCardStats($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT
                    COUNT(*) as total_cards,
                    SUM(current_balance) as total_balance,
                    SUM(spending_limit) as total_limit,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cards,
                    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_cards,
                    AVG(spending_limit) as avg_limit
                FROM virtual_cards WHERE user_id = ?";
        
        $result = $db->query($sql, [$user_id]);
        return $result->fetch_assoc() ?: [
            'total_cards' => 0,
            'total_balance' => 0,
            'total_limit' => 0,
            'active_cards' => 0,
            'blocked_cards' => 0,
            'avg_limit' => 0
        ];
    } catch (Exception $e) {
        error_log("Error getting card stats: " . $e->getMessage());
        return [
            'total_cards' => 0,
            'total_balance' => 0,
            'total_limit' => 0,
            'active_cards' => 0,
            'blocked_cards' => 0,
            'avg_limit' => 0
        ];
    }
}

/**
 * Get user's beneficiaries
 */
function getUserBeneficiaries($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY is_favorite DESC, name ASC";
        $result = $db->query($sql, [$user_id]);
        $beneficiaries = [];
        while ($row = $result->fetch_assoc()) {
            $beneficiaries[] = $row;
        }
        return $beneficiaries;
    } catch (Exception $e) {
        error_log("Error getting beneficiaries: " . $e->getMessage());
        return [];
    }
}

/**
 * Get beneficiary statistics
 */
function getBeneficiaryStats($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT 
                    COUNT(*) as total_beneficiaries,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count,
                    COUNT(DISTINCT country) as countries_count
                FROM beneficiaries WHERE user_id = ?";
        
        $result = $db->query($sql, [$user_id]);
        return $result->fetch_assoc() ?: [
            'total_beneficiaries' => 0,
            'favorite_count' => 0,
            'countries_count' => 0
        ];
    } catch (Exception $e) {
        error_log("Error getting beneficiary stats: " . $e->getMessage());
        return ['total_beneficiaries' => 0, 'favorite_count' => 0, 'countries_count' => 0];
    }
}

/**
 * Get monthly statement summaries
 */
function getMonthlyStatements($user_id, $limit = 12) {
    try {
        $db = getDB();
        $sql = "SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as statement_month,
                    DATE_FORMAT(created_at, '%M %Y') as month_name,
                    COUNT(*) as transaction_count,
                    SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as total_credits,
                    SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as total_debits,
                    SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE -amount END) as net_change
                FROM transactions 
                WHERE user_id = ? 
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY statement_month DESC
                LIMIT ?";
        
        $result = $db->query($sql, [$user_id, $limit]);
        $statements = [];
        while ($row = $result->fetch_assoc()) {
            $statements[] = $row;
        }
        return $statements;
    } catch (Exception $e) {
        error_log("Error getting monthly statements: " . $e->getMessage());
        return [];
    }
}

/**
 * Get account summary statistics
 */
function getAccountSummaryStats($user_id) {
    try {
        $db = getDB();
        $sql = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as lifetime_credits,
                    SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as lifetime_debits,
                    AVG(CASE WHEN transaction_type = 'credit' THEN amount ELSE NULL END) as avg_credit,
                    AVG(CASE WHEN transaction_type = 'debit' THEN amount ELSE NULL END) as avg_debit
                FROM transactions WHERE user_id = ?";
        
        $result = $db->query($sql, [$user_id]);
        return $result->fetch_assoc() ?: [
            'total_transactions' => 0,
            'lifetime_credits' => 0,
            'lifetime_debits' => 0,
            'avg_credit' => 0,
            'avg_debit' => 0
        ];
    } catch (Exception $e) {
        error_log("Error getting account summary: " . $e->getMessage());
        return [
            'total_transactions' => 0,
            'lifetime_credits' => 0,
            'lifetime_debits' => 0,
            'avg_credit' => 0,
            'avg_debit' => 0
        ];
    }
}

/**
 * Get user's recent login history
 */
function getUserLoginHistory($user_id, $limit = 10) {
    try {
        $db = getDB();
        $sql = "SELECT * FROM activity_logs 
                WHERE user_id = ? AND action LIKE '%login%' 
                ORDER BY created_at DESC LIMIT ?";
        
        $result = $db->query($sql, [$user_id, $limit]);
        $history = [];
        while ($row = $result->fetch_assoc()) {
            $history[] = $row;
        }
        return $history;
    } catch (Exception $e) {
        error_log("Error getting login history: " . $e->getMessage());
        return [];
    }
}

/**
 * Calculate user security score
 */
function calculateSecurityScore($user_data) {
    $score = 0;
    $items = [];
    
    // Password strength (assume strong if recently changed)
    $score += 25;
    $items[] = ['item' => 'Strong Password', 'status' => 'good', 'points' => 25];
    
    // Two-factor authentication
    if ($user_data['two_factor_enabled']) {
        $score += 30;
        $items[] = ['item' => 'Two-Factor Authentication', 'status' => 'good', 'points' => 30];
    } else {
        $items[] = ['item' => 'Two-Factor Authentication', 'status' => 'warning', 'points' => 0];
    }
    
    // Email verification (assume verified)
    $score += 20;
    $items[] = ['item' => 'Email Verified', 'status' => 'good', 'points' => 20];
    
    // Phone verification
    if (!empty($user_data['phone'])) {
        $score += 15;
        $items[] = ['item' => 'Phone Verified', 'status' => 'good', 'points' => 15];
    } else {
        $items[] = ['item' => 'Phone Verification', 'status' => 'warning', 'points' => 0];
    }
    
    // Recent activity (if logged in recently)
    if (strtotime($user_data['last_login']) > strtotime('-7 days')) {
        $score += 10;
        $items[] = ['item' => 'Recent Activity', 'status' => 'good', 'points' => 10];
    }
    
    return ['score' => $score, 'items' => $items];
}

/**
 * Format currency amount
 */
function formatCurrency($amount, $currency = 'USD') {
    return number_format($amount, 2);
}

/**
 * Get card type configuration
 */
function getCardTypeConfig($card_type = 'visa') {
    $card_types = [
        'visa' => ['name' => 'Visa', 'color' => '#1a1f71', 'icon' => 'fab fa-cc-visa'],
        'mastercard' => ['name' => 'Mastercard', 'color' => '#eb001b', 'icon' => 'fab fa-cc-mastercard'],
        'amex' => ['name' => 'American Express', 'color' => '#006fcf', 'icon' => 'fab fa-cc-amex'],
        'discover' => ['name' => 'Discover', 'color' => '#ff6000', 'icon' => 'fab fa-cc-discover']
    ];
    
    return $card_types[$card_type] ?? $card_types['visa'];
}
?>
