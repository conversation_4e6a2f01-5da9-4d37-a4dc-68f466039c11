/* ===== TRANSACTIONS PAGE STYLES ===== */

/* Page Layout */
.transaction-history-section {
    background: white;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.transaction-history-section.full-width {
    width: 100%;
    margin-top: 2rem;
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--background-light);
}

.section-title h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.section-title p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.section-actions {
    display: flex;
    gap: 1rem;
}

/* Filters Section */
.filters-section {
    margin-bottom: 2rem;
}

.filter-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.filter-group .form-control {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.filter-group .form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Transaction List */
.transaction-list-container {
    padding: 0;
}

.transaction-list {
    display: flex;
    flex-direction: column;
}

.enhanced-transaction-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.2s ease;
    cursor: pointer;
}

.enhanced-transaction-item:last-child {
    border-bottom: none;
}

.enhanced-transaction-item:hover {
    background: rgba(37, 99, 235, 0.02);
}

/* Transaction Avatar */
.transaction-avatar {
    flex-shrink: 0;
}

.avatar-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.avatar-icon.received {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
}

.avatar-icon.sent {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

/* Transaction Details */
.transaction-main {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.transaction-details {
    flex: 1;
}

.transaction-primary {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.25rem;
}

.transaction-primary h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.transaction-type {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.transaction-secondary {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.transaction-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.transaction-id {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', monospace;
}

/* Transaction Amount Section */
.transaction-amount-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    text-align: right;
    min-width: 120px;
}

.amount {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.2;
}

.amount.positive {
    color: var(--accent-color);
}

.amount.negative {
    color: var(--danger-color);
}

.transaction-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.transaction-status {
    margin-top: 0.25rem;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-color);
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.failed {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Transaction Expand Icon */
.transaction-expand-icon {
    flex-shrink: 0;
    color: var(--text-muted);
    transition: all 0.2s ease;
}

.enhanced-transaction-item:hover .transaction-expand-icon {
    color: var(--primary-color);
    transform: translateX(2px);
}

/* Empty State */
.empty-transactions {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-transactions .empty-icon {
    width: 64px;
    height: 64px;
    color: var(--text-muted);
    margin: 0 auto 1.5rem;
}

.empty-transactions h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.empty-transactions p {
    font-size: 0.875rem;
    margin: 0 0 2rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Pagination */
.transaction-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-light);
    background: var(--background-light);
}

.transaction-summary {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.pagination {
    display: flex;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    background: var(--background-light);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

@media (max-width: 768px) {
    .enhanced-transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
    }
    
    .transaction-main {
        width: 100%;
    }
    
    .transaction-amount-section {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        text-align: left;
    }
    
    .transaction-expand-icon {
        display: none;
    }
    
    .transaction-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .pagination {
        width: 100%;
        justify-content: center;
    }
    
    .filter-card form {
        flex-direction: column;
        gap: 1rem;
    }
    
    .filter-group {
        min-width: auto;
    }
}
