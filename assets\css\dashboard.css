/* ===== DASHBOARD SPECIFIC STYLES ===== */

/* Account Info Styles */
.account-info {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.account-details h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.account-details p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 1rem 0;
    font-family: 'Monaco', 'Menlo', monospace;
}

.account-status {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.balance-info {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: var(--radius-xl);
}

.balance-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 0.5rem 0;
}

.balance-info h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.balance-info p:last-child {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin: 0;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quick-actions .btn {
    justify-content: flex-start;
    padding: 0.75rem 1rem;
}

.quick-actions .btn svg {
    width: 16px;
    height: 16px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-state svg {
    width: 48px;
    height: 48px;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    font-size: 0.875rem;
    margin: 0 0 1.5rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Text Colors */
.text-success {
    color: var(--accent-color) !important;
    font-weight: 600;
}

.text-danger {
    color: var(--danger-color) !important;
    font-weight: 600;
}

.text-warning {
    color: var(--warning-color) !important;
    font-weight: 600;
}

/* Dashboard Grid Layout */
.banking-dashboard {
    display: block;
    margin-top: 2rem;
}

.account-overview-card {
    margin-bottom: 2rem;
}

.transactions-card {
    margin-top: 2rem;
    width: 100%;
}

/* Responsive Dashboard */
@media (max-width: 1200px) {
    .account-info {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }
    
    .quick-actions {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .banking-dashboard {
        margin-top: 1.5rem;
    }

    .account-overview-card {
        margin-bottom: 1.5rem;
    }

    .transactions-card {
        margin-top: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .account-info {
        gap: 1rem;
    }

    .balance-info h1 {
        font-size: 2rem;
    }

    .quick-actions {
        flex-direction: column;
    }

    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Enhanced Card Styles for Dashboard */
.card {
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
}

/* Table Enhancements */
.table-container {
    margin: 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.table th {
    background: var(--background-light);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-secondary);
}

.table td {
    font-size: 0.875rem;
    vertical-align: middle;
}

.table tr:hover {
    background: rgba(37, 99, 235, 0.02);
}

/* Loading States */
.card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.5s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* Hover Effects */
.quick-actions .btn:hover {
    transform: translateX(4px);
}

.table tr:hover td {
    color: var(--text-primary);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.status-active {
    color: var(--accent-color);
}

.status-pending {
    color: var(--warning-color);
}

.status-inactive {
    color: var(--text-muted);
}
