<?php
// Set page variables
$page_title = 'Transaction Statements';
$additional_css = ['dashboard-clean.css'];
$additional_js = ['dashboard.js'];

// Include header template
require_once '../../templates/user/header.php';

// Include database connection
require_once '../../config/config.php';
requireLogin();

// Get user account information and statements
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user account details
    $account_sql = "SELECT * FROM accounts WHERE id = ?";
    $account_result = $db->query($account_sql, [$user_id]);
    $account = $account_result->fetch_assoc();
    
    // Get monthly statement summaries for admin-created transactions
    $statements_sql = "SELECT
                        DATE_FORMAT(at.created_at, '%Y-%m') as statement_month,
                        DATE_FORMAT(at.created_at, '%M %Y') as month_name,
                        COUNT(*) as transaction_count,
                        SUM(CASE WHEN at.transaction_type = 'credit' THEN at.amount ELSE 0 END) as total_credits,
                        SUM(CASE WHEN at.transaction_type = 'debit' THEN at.amount ELSE 0 END) as total_debits,
                        SUM(CASE WHEN at.transaction_type = 'credit' THEN at.amount ELSE -at.amount END) as net_change
                       FROM account_transactions at
                       WHERE at.account_id = ?
                       GROUP BY DATE_FORMAT(at.created_at, '%Y-%m')
                       ORDER BY statement_month DESC
                       LIMIT 12";
    $statements_result = $db->query($statements_sql, [$user_id]);
    $statements = [];
    while ($row = $statements_result->fetch_assoc()) {
        $statements[] = $row;
    }

    // Get current month admin transactions for detailed view
    $current_month = date('Y-m');
    $current_transactions_sql = "SELECT at.*,
                                        admin.first_name as admin_first_name,
                                        admin.last_name as admin_last_name
                                FROM account_transactions at
                                LEFT JOIN accounts admin ON at.processed_by = admin.id
                                WHERE at.account_id = ? AND DATE_FORMAT(at.created_at, '%Y-%m') = ?
                                ORDER BY at.created_at DESC";
    $current_transactions_result = $db->query($current_transactions_sql, [$user_id, $current_month]);
    $current_transactions = [];
    while ($row = $current_transactions_result->fetch_assoc()) {
        $current_transactions[] = $row;
    }
    
    // Get account summary statistics
    $summary_sql = "SELECT 
                     COUNT(*) as total_transactions,
                     SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as lifetime_credits,
                     SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as lifetime_debits,
                     AVG(CASE WHEN transaction_type = 'credit' THEN amount ELSE NULL END) as avg_credit,
                     AVG(CASE WHEN transaction_type = 'debit' THEN amount ELSE NULL END) as avg_debit
                    FROM transactions WHERE user_id = ?";
    $summary_result = $db->query($summary_sql, [$user_id]);
    $summary = $summary_result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Statements page error: " . $e->getMessage());
    $account = $_SESSION;
    $statements = [];
    $current_transactions = [];
    $summary = [
        'total_transactions' => 0,
        'lifetime_credits' => 0,
        'lifetime_debits' => 0,
        'avg_credit' => 0,
        'avg_debit' => 0
    ];
}
?>

<!-- Include Sidebar -->
<?php require_once '../../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">Transaction Statements</h1>
                    <p class="text-muted">View admin-created transactions and account adjustments</p>
                    <small class="text-info">
                        <i class="fas fa-info-circle me-1"></i>
                        This page shows transactions created by bank administrators.
                        For transfer history, visit <a href="../transfers/">Transfer History</a>.
                    </small>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>Download Current
                    </button>
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>Generate PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-receipt" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($summary['total_transactions']); ?></h3>
                    <p class="text-muted mb-0">Total Transactions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-arrow-up" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1">$<?php echo number_format($summary['lifetime_credits'], 2); ?></h3>
                    <p class="text-muted mb-0">Total Credits</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-arrow-down" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1">$<?php echo number_format($summary['lifetime_debits'], 2); ?></h3>
                    <p class="text-muted mb-0">Total Debits</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-balance-scale" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1">$<?php echo number_format($account['balance'], 2); ?></h3>
                    <p class="text-muted mb-0">Current Balance</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Statements -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Monthly Statements
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($statements)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">No Statements Available</h5>
                            <p class="text-muted">Your statements will appear here once you have transactions</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Statement Period</th>
                                        <th>Transactions</th>
                                        <th>Credits</th>
                                        <th>Debits</th>
                                        <th>Net Change</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($statements as $statement): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($statement['month_name']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo $statement['transaction_count']; ?></span>
                                            </td>
                                            <td class="text-success">
                                                $<?php echo number_format($statement['total_credits'], 2); ?>
                                            </td>
                                            <td class="text-danger">
                                                $<?php echo number_format($statement['total_debits'], 2); ?>
                                            </td>
                                            <td class="<?php echo $statement['net_change'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $statement['net_change'] >= 0 ? '+' : ''; ?>$<?php echo number_format($statement['net_change'], 2); ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="viewStatement('<?php echo $statement['statement_month']; ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="downloadStatement('<?php echo $statement['statement_month']; ?>')">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Current Month Details -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Current Month (<?php echo date('F Y'); ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($current_transactions)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-times text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No transactions this month</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach (array_slice($current_transactions, 0, 5) as $transaction): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($transaction['description']); ?></h6>
                                            <small class="text-muted d-block">
                                                <?php echo date('M j, g:i A', strtotime($transaction['created_at'])); ?>
                                            </small>
                                            <?php if ($transaction['admin_first_name']): ?>
                                            <small class="text-info">
                                                <i class="fas fa-user-shield me-1"></i>
                                                By: <?php echo htmlspecialchars($transaction['admin_first_name'] . ' ' . $transaction['admin_last_name']); ?>
                                            </small>
                                            <?php endif; ?>
                                            <div class="mt-1">
                                                <span class="badge bg-<?php echo $transaction['category'] === 'adjustment' ? 'warning' : 'info'; ?> badge-sm">
                                                    <?php echo ucfirst($transaction['category']); ?>
                                                </span>
                                                <?php if ($transaction['reference_number']): ?>
                                                <span class="badge bg-secondary badge-sm ms-1">
                                                    Ref: <?php echo htmlspecialchars($transaction['reference_number']); ?>
                                                </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="<?php echo $transaction['transaction_type'] === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $transaction['transaction_type'] === 'credit' ? '+' : '-'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($current_transactions) > 5): ?>
                            <div class="text-center mt-3">
                                <a href="<?php echo $base_url; ?>/dashboard/transactions/" class="btn btn-sm btn-outline-primary">
                                    View All Transactions
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo $base_url; ?>/dashboard/transactions/" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-2"></i>View All Transactions
                        </a>
                        <a href="<?php echo $base_url; ?>/dashboard/transfers/" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-paper-plane me-2"></i>Make Transfer
                        </a>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="exportStatements()">
                            <i class="fas fa-file-export me-2"></i>Export Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewStatement(month) {
    // Implementation for viewing statement details
    alert('View statement for ' + month);
}

function downloadStatement(month) {
    // Implementation for downloading statement
    alert('Download statement for ' + month);
}

function exportStatements() {
    // Implementation for exporting all statements
    alert('Export all statements');
}
</script>

<?php
// Include footer template
require_once '../../templates/user/footer.php';
?>
