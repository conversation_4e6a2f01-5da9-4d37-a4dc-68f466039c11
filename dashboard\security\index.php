<?php
// Set page variables
$page_title = 'Security Center';
$additional_css = ['dashboard.css'];
$additional_js = ['dashboard.js'];

// Include header template
require_once '../../templates/user/header.php';

// Include database connection
require_once '../../config/config.php';
requireLogin();

// Handle security actions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'change_password':
                    $current_password = $_POST['current_password'];
                    $new_password = $_POST['new_password'];
                    $confirm_password = $_POST['confirm_password'];
                    
                    // Validation
                    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                        throw new Exception('All password fields are required.');
                    }
                    
                    if ($new_password !== $confirm_password) {
                        throw new Exception('New passwords do not match.');
                    }
                    
                    if (strlen($new_password) < 8) {
                        throw new Exception('New password must be at least 8 characters long.');
                    }
                    
                    // Verify current password
                    $user_sql = "SELECT password FROM accounts WHERE id = ?";
                    $user_result = $db->query($user_sql, [$user_id]);
                    $user = $user_result->fetch_assoc();
                    
                    if (!verifyPassword($current_password, $user['password'])) {
                        throw new Exception('Current password is incorrect.');
                    }
                    
                    // Update password
                    $hashed_password = hashPassword($new_password);
                    $update_sql = "UPDATE accounts SET password = ?, updated_at = NOW() WHERE id = ?";
                    $db->query($update_sql, [$hashed_password, $user_id]);
                    
                    // Log activity
                    logActivity($user_id, 'Password changed', 'accounts', $user_id);
                    
                    $success_message = 'Password changed successfully!';
                    break;
                    
                case 'toggle_otp':
                    // Include user 2FA helper functions
                    require_once '../../includes/user-2fa-functions.php';

                    $enable_otp = isset($_POST['enable_otp']) ? 1 : 0;

                    // Update OTP setting in user_security_settings
                    if (updateUserSecuritySetting($user_id, 'otp_enabled', $enable_otp, $user_id)) {
                        // Sync with accounts table
                        syncUserTwoFactorSettings($user_id);

                        // Log activity
                        $action_text = $enable_otp ? 'enabled' : 'disabled';
                        logActivity($user_id, "OTP verification {$action_text}", 'user_security_settings', $user_id);

                        $success_message = "OTP verification {$action_text} successfully!";
                    } else {
                        throw new Exception('Failed to update OTP setting. Please try again.');
                    }
                    break;

                case 'toggle_2fa_requirement':
                    // Include user 2FA helper functions
                    require_once '../../includes/user-2fa-functions.php';

                    $require_2fa = isset($_POST['require_2fa']) ? 1 : 0;

                    // Update 2FA requirement setting
                    if (updateUserSecuritySetting($user_id, 'require_2fa', $require_2fa, $user_id)) {
                        // Sync with accounts table
                        syncUserTwoFactorSettings($user_id);

                        // Log activity
                        $action_text = $require_2fa ? 'enabled' : 'disabled';
                        logActivity($user_id, "2FA requirement {$action_text}", 'user_security_settings', $user_id);

                        $success_message = "2FA requirement {$action_text} successfully!";
                    } else {
                        throw new Exception('Failed to update 2FA requirement. Please try again.');
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user security information
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Include user 2FA helper functions
    require_once '../../includes/user-2fa-functions.php';

    // Get user account details
    $account_sql = "SELECT id, username, email, phone, two_factor_enabled, last_login, created_at FROM accounts WHERE id = ?";
    $account_result = $db->query($account_sql, [$user_id]);
    $account = $account_result->fetch_assoc();

    // Get detailed security settings
    $security_settings = getUserSecuritySettings($user_id);
    $two_factor_status = getUserTwoFactorStatus($user_id);
    
    // Get recent login history
    $login_history_sql = "SELECT * FROM activity_logs 
                         WHERE user_id = ? AND action LIKE '%login%' 
                         ORDER BY created_at DESC LIMIT 10";
    $login_history_result = $db->query($login_history_sql, [$user_id]);
    $login_history = [];
    while ($row = $login_history_result->fetch_assoc()) {
        $login_history[] = $row;
    }
    
    // Calculate security score
    $security_score = 0;
    $security_items = [];
    
    // Password strength (assume strong if recently changed)
    $security_score += 25;
    $security_items[] = ['item' => 'Strong Password', 'status' => 'good', 'points' => 25];
    
    // Two-factor authentication (OTP + requirement)
    if ($two_factor_status['is_protected']) {
        $security_score += 30;
        $security_items[] = ['item' => 'Two-Factor Authentication', 'status' => 'good', 'points' => 30];
    } else {
        $security_items[] = ['item' => 'Two-Factor Authentication', 'status' => 'warning', 'points' => 0];
    }

    // OTP verification
    if ($two_factor_status['otp_enabled']) {
        $security_score += 15;
        $security_items[] = ['item' => 'OTP Verification', 'status' => 'good', 'points' => 15];
    } else {
        $security_items[] = ['item' => 'OTP Verification', 'status' => 'warning', 'points' => 0];
    }

    // Google 2FA
    if ($two_factor_status['google_2fa_enabled']) {
        $security_score += 20;
        $security_items[] = ['item' => 'Google Authenticator', 'status' => 'good', 'points' => 20];
    } else {
        $security_items[] = ['item' => 'Google Authenticator', 'status' => 'info', 'points' => 0];
    }
    
    // Email verification (assume verified)
    $security_score += 15;
    $security_items[] = ['item' => 'Email Verified', 'status' => 'good', 'points' => 15];

    // Phone verification
    if (!empty($account['phone'])) {
        $security_score += 10;
        $security_items[] = ['item' => 'Phone Verified', 'status' => 'good', 'points' => 10];
    } else {
        $security_items[] = ['item' => 'Phone Verification', 'status' => 'warning', 'points' => 0];
    }
    
    // Recent activity (if logged in recently)
    if (strtotime($account['last_login']) > strtotime('-7 days')) {
        $security_score += 10;
        $security_items[] = ['item' => 'Recent Activity', 'status' => 'good', 'points' => 10];
    }
    
} catch (Exception $e) {
    error_log("Security page error: " . $e->getMessage());
    $account = $_SESSION;
    $login_history = [];
    $security_score = 0;
    $security_items = [];
}
?>

<!-- Include Sidebar -->
<?php require_once '../../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">Security Center</h1>
                    <p class="text-muted">Manage your account security settings and monitor activity</p>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-2">Security Score:</span>
                    <div class="progress" style="width: 100px; height: 8px;">
                        <div class="progress-bar <?php echo $security_score >= 80 ? 'bg-success' : ($security_score >= 60 ? 'bg-warning' : 'bg-danger'); ?>" 
                             style="width: <?php echo $security_score; ?>%"></div>
                    </div>
                    <span class="ms-2 fw-bold"><?php echo $security_score; ?>%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Security Overview -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Security Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php foreach ($security_items as $item): ?>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center p-3 border rounded">
                                    <div class="me-3">
                                        <?php if ($item['status'] === 'good'): ?>
                                            <i class="fas fa-check-circle text-success" style="font-size: 1.5rem;"></i>
                                        <?php else: ?>
                                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 1.5rem;"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($item['item']); ?></h6>
                                        <small class="text-muted">
                                            <?php echo $item['status'] === 'good' ? 'Enabled' : 'Not configured'; ?>
                                            • <?php echo $item['points']; ?> points
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#otpModal">
                            <i class="fas fa-envelope me-2"></i>
                            <?php echo $two_factor_status['otp_enabled'] ? 'Disable' : 'Enable'; ?> OTP
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#twoFactorModal">
                            <i class="fas fa-shield-alt me-2"></i>
                            <?php echo $two_factor_status['require_2fa'] ? 'Disable' : 'Enable'; ?> 2FA Requirement
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#googleAuthModal">
                            <i class="fab fa-google me-2"></i>Setup Google Auth
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-sign-out-alt me-2"></i>Sign Out All Devices
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-circle me-2"></i>Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Username</label>
                                <div class="fw-bold"><?php echo htmlspecialchars($account['username']); ?></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Email Address</label>
                                <div class="fw-bold"><?php echo htmlspecialchars($account['email']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Phone Number</label>
                                <div class="fw-bold"><?php echo htmlspecialchars($account['phone'] ?: 'Not provided'); ?></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Account Created</label>
                                <div class="fw-bold"><?php echo date('F j, Y', strtotime($account['created_at'])); ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Last Login</label>
                                <div class="fw-bold"><?php echo $account['last_login'] ? date('F j, Y g:i A', strtotime($account['last_login'])) : 'Never'; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Security Protection</label>
                                <div class="fw-bold">
                                    <?php if ($two_factor_status['is_protected']): ?>
                                        <span class="text-success"><i class="fas fa-shield-alt me-1"></i>Protected</span>
                                        <small class="d-block text-muted">
                                            OTP: <?php echo $two_factor_status['otp_enabled'] ? 'On' : 'Off'; ?> •
                                            2FA: <?php echo $two_factor_status['require_2fa'] ? 'Required' : 'Optional'; ?>
                                            <?php if ($two_factor_status['google_2fa_enabled']): ?>
                                                • Google Auth: On
                                            <?php endif; ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Basic Protection</span>
                                        <small class="d-block text-muted">Consider enabling 2FA for better security</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Activity
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($login_history)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-clock text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No recent activity</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach (array_slice($login_history, 0, 5) as $activity): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($activity['action']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, g:i A', strtotime($activity['created_at'])); ?>
                                            </small>
                                        </div>
                                        <i class="fas fa-shield-alt text-success"></i>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">
                    <i class="fas fa-key me-2"></i>Change Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="change_password">

                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>

                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="8">
                        <div class="form-text">Password must be at least 8 characters long.</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OTP Verification Modal -->
<div class="modal fade" id="otpModal" tabindex="-1" aria-labelledby="otpModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="otpModalLabel">
                    <i class="fas fa-envelope me-2"></i>OTP Verification
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="toggle_otp">

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enable_otp" name="enable_otp"
                                   <?php echo $two_factor_status['otp_enabled'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="enable_otp">
                                Enable OTP Verification via Email
                            </label>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        OTP (One-Time Password) verification sends a unique code to your email address during login for additional security.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>Update Setting
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 2FA Requirement Modal -->
<div class="modal fade" id="twoFactorModal" tabindex="-1" aria-labelledby="twoFactorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="twoFactorModalLabel">
                    <i class="fas fa-shield-alt me-2"></i>2FA Requirement
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="toggle_2fa_requirement">

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="require_2fa" name="require_2fa"
                                   <?php echo $two_factor_status['require_2fa'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="require_2fa">
                                Require Two-Factor Authentication for Login
                            </label>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        When enabled, you must complete 2FA verification (OTP or Google Authenticator) to access your account.
                        Make sure you have at least one 2FA method enabled before requiring it.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>Update Setting
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Google Authenticator Modal -->
<div class="modal fade" id="googleAuthModal" tabindex="-1" aria-labelledby="googleAuthModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="googleAuthModalLabel">
                    <i class="fab fa-google me-2"></i>Google Authenticator Setup
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Google Authenticator setup is coming soon! This will provide time-based codes for enhanced security.
                </div>

                <div class="text-center">
                    <i class="fab fa-google" style="font-size: 4rem; color: #4285f4;"></i>
                    <h6 class="mt-3">Enhanced Security with Google Authenticator</h6>
                    <p class="text-muted">Generate time-based verification codes on your mobile device</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
require_once '../../templates/user/footer.php';
?>
