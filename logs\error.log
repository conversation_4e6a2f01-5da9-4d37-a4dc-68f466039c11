[2025-06-02 09:53:14] WARNING: mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php:200
User: 1 (IP: ::1)
Request: POST /online_banking/admin/email-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(200): mail()
#3 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(115): PHPMailer\PHPMailer\PHPMailer->sendViaMail()
#4 C:\MAMP\htdocs\online_banking\admin\email-settings.php(225): PHPMailer\PHPMailer\PHPMailer->send()
#5 C:\MAMP\htdocs\online_banking\admin\email-settings.php(95): sendTestEmail()
---
[2025-06-02 09:54:01] WARNING: mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php:163
User: 1 (IP: ::1)
Request: POST /online_banking/admin/email-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(163): mail()
#3 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(113): PHPMailer\PHPMailer\PHPMailer->sendViaSMTP()
#4 C:\MAMP\htdocs\online_banking\admin\email-settings.php(225): PHPMailer\PHPMailer\PHPMailer->send()
#5 C:\MAMP\htdocs\online_banking\admin\email-settings.php(95): sendTestEmail()
---
[2025-06-02 09:57:07] WARNING: mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php:163
User: 1 (IP: ::1)
Request: POST /online_banking/admin/email-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(163): mail()
#3 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(113): PHPMailer\PHPMailer\PHPMailer->sendViaSMTP()
#4 C:\MAMP\htdocs\online_banking\admin\email-settings.php(225): PHPMailer\PHPMailer\PHPMailer->send()
#5 C:\MAMP\htdocs\online_banking\admin\email-settings.php(95): sendTestEmail()
---
[2025-06-02 10:21:19] WARNING: mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php:163
User: 1 (IP: ::1)
Request: POST /online_banking/admin/email-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(163): mail()
#3 C:\MAMP\htdocs\online_banking\vendor\phpmailer\phpmailer\src\PHPMailer.php(113): PHPMailer\PHPMailer\PHPMailer->sendViaSMTP()
#4 C:\MAMP\htdocs\online_banking\admin\email-settings.php(225): PHPMailer\PHPMailer\PHPMailer->send()
#5 C:\MAMP\htdocs\online_banking\admin\email-settings.php(95): sendTestEmail()
---
[2025-06-02 10:26:41] EXCEPTION: Class "Sonata\GoogleAuthenticator\GoogleAuthenticator" not found in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:14
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:34:44] EXCEPTION: Class "Sonata\GoogleAuthenticator\GoogleAuthenticator" not found in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:14
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:37:23] EXCEPTION: Class "Sonata\GoogleAuthenticator\GoogleAuthenticator" not found in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:14
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:40:30] EXCEPTION: Class "Sonata\GoogleAuthenticator\GoogleAuthenticator" not found in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:14
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:44:17] EXCEPTION: Class "Sonata\GoogleAuthenticator\GoogleAuthenticator" not found in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:14
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:48:34] EXCEPTION: Call to undefined method Database::prepare() in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:78
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:49:31] EXCEPTION: Call to undefined method Database::prepare() in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:78
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:53:00] EXCEPTION: Call to undefined method Database::prepare() in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:78
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 10:54:11] EXCEPTION: Call to undefined method Database::prepare() in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:78
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 11:46:17] WARNING: Undefined variable $db in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:54
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(54): bankingErrorHandler()
---
[2025-06-02 11:46:17] EXCEPTION: Call to a member function query() on null in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:54
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 11:47:37] WARNING: include(../admin/includes/header.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:85
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(85): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(85): include()
---
[2025-06-02 11:47:37] WARNING: include(): Failed opening '../admin/includes/header.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:85
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(85): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(85): include()
---
[2025-06-02 11:47:37] WARNING: include(../admin/includes/sidebar.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:89
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(89): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(89): include()
---
[2025-06-02 11:47:37] WARNING: include(): Failed opening '../admin/includes/sidebar.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:89
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(89): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(89): include()
---
[2025-06-02 11:47:37] WARNING: include(../admin/includes/footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:277
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(277): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(277): include()
---
[2025-06-02 11:47:37] WARNING: include(): Failed opening '../admin/includes/footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\configure-2fa.php:277
User: 1 (IP: ::1)
Request: GET /online_banking/admin/configure-2fa.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(277): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\configure-2fa.php(277): include()
---
[2025-06-02 11:47:43] WARNING: include(../admin/includes/header.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:125
User: 1 (IP: ::1)
Request: GET /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): include()
---
[2025-06-02 11:47:43] WARNING: include(): Failed opening '../admin/includes/header.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:125
User: 1 (IP: ::1)
Request: GET /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): include()
---
[2025-06-02 11:47:43] WARNING: include(../admin/includes/sidebar.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:129
User: 1 (IP: ::1)
Request: GET /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): include()
---
[2025-06-02 11:47:43] WARNING: include(): Failed opening '../admin/includes/sidebar.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:129
User: 1 (IP: ::1)
Request: GET /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): include()
---
[2025-06-02 11:47:43] WARNING: include(../admin/includes/footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:294
User: 1 (IP: ::1)
Request: GET /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): include()
---
[2025-06-02 11:47:43] WARNING: include(): Failed opening '../admin/includes/footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:294
User: 1 (IP: ::1)
Request: GET /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): include()
---
[2025-06-02 11:47:57] WARNING: include(../admin/includes/header.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:125
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): include()
---
[2025-06-02 11:47:57] WARNING: include(): Failed opening '../admin/includes/header.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:125
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): include()
---
[2025-06-02 11:47:57] WARNING: include(../admin/includes/sidebar.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:129
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): include()
---
[2025-06-02 11:47:57] WARNING: include(): Failed opening '../admin/includes/sidebar.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:129
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): include()
---
[2025-06-02 11:47:57] WARNING: include(../admin/includes/footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:294
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): include()
---
[2025-06-02 11:47:57] WARNING: include(): Failed opening '../admin/includes/footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:294
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): include()
---
[2025-06-02 11:49:27] WARNING: include(../admin/includes/header.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:125
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): include()
---
[2025-06-02 11:49:27] WARNING: include(): Failed opening '../admin/includes/header.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:125
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(125): include()
---
[2025-06-02 11:49:27] WARNING: include(../admin/includes/sidebar.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:129
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): include()
---
[2025-06-02 11:49:27] WARNING: include(): Failed opening '../admin/includes/sidebar.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:129
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(129): include()
---
[2025-06-02 11:49:27] WARNING: include(../admin/includes/footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:294
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): include()
---
[2025-06-02 11:49:27] WARNING: include(): Failed opening '../admin/includes/footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php:294
User: 1 (IP: ::1)
Request: POST /online_banking/admin/google-2fa-setup.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\google-2fa-setup.php(294): include()
---
[2025-06-02 12:24:24] WARNING: include(../includes/admin_sidebar_header.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\view-user.php:74
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): include()
---
[2025-06-02 12:24:24] WARNING: include(): Failed opening '../includes/admin_sidebar_header.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\view-user.php:74
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): include()
---
[2025-06-02 12:24:24] WARNING: include(../includes/admin_sidebar_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\view-user.php:625
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): include()
---
[2025-06-02 12:24:24] WARNING: include(): Failed opening '../includes/admin_sidebar_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\view-user.php:625
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): include()
---
[2025-06-02 12:24:38] WARNING: include(../includes/admin_sidebar_header.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\view-user.php:74
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): include()
---
[2025-06-02 12:24:38] WARNING: include(): Failed opening '../includes/admin_sidebar_header.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\view-user.php:74
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(74): include()
---
[2025-06-02 12:24:38] WARNING: include(../includes/admin_sidebar_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\view-user.php:625
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): include()
---
[2025-06-02 12:24:38] WARNING: include(): Failed opening '../includes/admin_sidebar_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\view-user.php:625
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user.php?id=5
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user.php(625): include()
---
[2025-06-02 14:36:29] WARNING: Undefined array key "role" in C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php:7
User: 1 (IP: ::1)
Request: GET /online_banking/admin/ajax/get-document-details.php?id=1
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php(7): bankingErrorHandler()
---
[2025-06-02 14:38:30] WARNING: Undefined array key "role" in C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php:7
User: 1 (IP: ::1)
Request: GET /online_banking/admin/ajax/get-document-details.php?id=1
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php(7): bankingErrorHandler()
---
[2025-06-02 14:38:33] WARNING: Undefined array key "role" in C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php:7
User: 1 (IP: ::1)
Request: GET /online_banking/admin/ajax/get-document-details.php?id=1
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php(7): bankingErrorHandler()
---
[2025-06-02 14:38:46] WARNING: Undefined array key "role" in C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php:7
User: 1 (IP: ::1)
Request: GET /online_banking/admin/ajax/get-document-details.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\ajax\get-document-details.php(7): bankingErrorHandler()
---
[2025-06-02 14:53:50] WARNING: require_once(../../config/config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\ajax\get_user_document_details.php:2
User: guest (IP: ::1)
Request: GET /online_banking/test_document_check.php?id=f954f4fc-adc8-4f4c-b05a-ffee2dd83394&vscodeBrowserReqId=*************
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\ajax\get_user_document_details.php(2): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\ajax\get_user_document_details.php(2): require_once()
#3 C:\MAMP\htdocs\online_banking\test_document_check.php(37): include()
---
[2025-06-02 14:53:50] EXCEPTION: Failed opening required '../../config/config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\ajax\get_user_document_details.php:2
User: guest (IP: ::1)
Request: GET /online_banking/test_document_check.php?id=f954f4fc-adc8-4f4c-b05a-ffee2dd83394&vscodeBrowserReqId=*************
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\test_document_check.php(37): include()\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-02 16:43:58] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php:62
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php(62): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(54): include()
---
[2025-06-02 16:43:58] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php:62
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php(62): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(54): include()
---
[2025-06-02 16:43:58] WARNING: include(../includes/components/user-documents-section.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\view-user-new.php:57
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): include()
---
[2025-06-02 16:43:58] WARNING: include(): Failed opening '../includes/components/user-documents-section.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\view-user-new.php:57
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): include()
---
[2025-06-02 16:44:01] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php:62
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php(62): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(54): include()
---
[2025-06-02 16:44:01] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php:62
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\includes\components\user-cards-crypto-section.php(62): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(54): include()
---
[2025-06-02 16:44:01] WARNING: include(../includes/components/user-documents-section.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\view-user-new.php:57
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): include()
---
[2025-06-02 16:44:01] WARNING: include(): Failed opening '../includes/components/user-documents-section.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\view-user-new.php:57
User: 1 (IP: ::1)
Request: GET /online_banking/admin/view-user-new.php?id=3
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\view-user-new.php(57): include()
---
{"timestamp":"2025-06-02 17:01:49","level":"INFO","message":"Test error message","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"test":true}}
{"timestamp":"2025-06-02 17:01:49","level":"WARNING","message":"Input validation failed","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"errors":{"email":"Invalid email format","amount":"Must be at least 0","name":"This field is required"},"data_keys":["email","amount","name"]}}
{"timestamp":"2025-06-02 17:01:49","level":"INFO","message":"New session initiated","session_id":"736dc1f51d72894115e13c6210417d80","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"session_id":"736dc1f51d72894115e13c6210417d80","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:01:49","level":"DEBUG","message":"Session data set","session_id":"736dc1f51d72894115e13c6210417d80","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":"unknown"}}
{"timestamp":"2025-06-02 17:01:49","level":"DEBUG","message":"Session data removed","session_id":"736dc1f51d72894115e13c6210417d80","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":"unknown"}}
{"timestamp":"2025-06-02 17:01:49","level":"INFO","message":"User logged in","session_id":"a43c59cc353ff9f7d0d56bf64174acff","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":123,"user_type":"admin","session_id":"a43c59cc353ff9f7d0d56bf64174acff","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:01:49","level":"ERROR","message":"Exception: Call to undefined method Database::prepare() in C:\\MAMP\\htdocs\\online_banking\\config\\AuditLogger.php:166","session_id":"a43c59cc353ff9f7d0d56bf64174acff","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 C:\\MAMP\\htdocs\\online_banking\\config\\AuditLogger.php(140): AuditLogger->logToDatabase(Array)\n#1 C:\\MAMP\\htdocs\\online_banking\\test\\admin\\test-phase2-security.php(223): AuditLogger::log('ADMIN', 'Test admin acti...', Array)\n#2 C:\\MAMP\\htdocs\\online_banking\\test\\admin\\test-phase2-security.php(47): {closure}()\n#3 C:\\MAMP\\htdocs\\online_banking\\test\\admin\\test-phase2-security.php(209): runTest('AuditLogger Cla...', Object(Closure))\n#4 {main}"}}
{"timestamp":"2025-06-02 17:04:26","level":"INFO","message":"Test error message","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"test":true}}
{"timestamp":"2025-06-02 17:04:26","level":"WARNING","message":"Input validation failed","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"errors":{"email":"Invalid email format","amount":"Must be at least 0","name":"This field is required"},"data_keys":["email","amount","name"]}}
{"timestamp":"2025-06-02 17:04:26","level":"INFO","message":"New session initiated","session_id":"1c6e2dfbfcd879f2ef45d9e641a200a8","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"session_id":"1c6e2dfbfcd879f2ef45d9e641a200a8","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:04:26","level":"DEBUG","message":"Session data set","session_id":"1c6e2dfbfcd879f2ef45d9e641a200a8","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":"unknown"}}
{"timestamp":"2025-06-02 17:04:26","level":"DEBUG","message":"Session data removed","session_id":"1c6e2dfbfcd879f2ef45d9e641a200a8","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":"unknown"}}
{"timestamp":"2025-06-02 17:04:26","level":"INFO","message":"User logged in","session_id":"60cdbfa4b095ca6a2a99616e72fd8c82","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":123,"user_type":"admin","session_id":"60cdbfa4b095ca6a2a99616e72fd8c82","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:04:26","level":"ERROR","message":"Exception: Call to undefined method Database::prepare() in C:\\MAMP\\htdocs\\online_banking\\config\\AuditLogger.php:166","session_id":"60cdbfa4b095ca6a2a99616e72fd8c82","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 C:\\MAMP\\htdocs\\online_banking\\config\\AuditLogger.php(140): AuditLogger->logToDatabase(Array)\n#1 C:\\MAMP\\htdocs\\online_banking\\test\\admin\\test-phase2-security.php(227): AuditLogger::log('ADMIN', 'Test admin acti...', Array)\n#2 C:\\MAMP\\htdocs\\online_banking\\test\\admin\\test-phase2-security.php(47): {closure}()\n#3 C:\\MAMP\\htdocs\\online_banking\\test\\admin\\test-phase2-security.php(209): runTest('AuditLogger Cla...', Object(Closure))\n#4 {main}"}}
{"timestamp":"2025-06-02 17:13:41","level":"INFO","message":"Test error message","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"test":true}}
{"timestamp":"2025-06-02 17:13:41","level":"WARNING","message":"Input validation failed","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"errors":{"email":"Invalid email format","amount":"Must be at least 0","name":"This field is required"},"data_keys":["email","amount","name"]}}
{"timestamp":"2025-06-02 17:13:41","level":"INFO","message":"Session ID regenerated","session_id":"8392c377308ac68901476bd578010054","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"old_session_id":"60cdbfa4b095ca6a2a99616e72fd8c82","new_session_id":"8392c377308ac68901476bd578010054","user_id":123}}
{"timestamp":"2025-06-02 17:13:41","level":"DEBUG","message":"Session data set","session_id":"8392c377308ac68901476bd578010054","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":123}}
{"timestamp":"2025-06-02 17:13:41","level":"DEBUG","message":"Session data removed","session_id":"8392c377308ac68901476bd578010054","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":123}}
{"timestamp":"2025-06-02 17:13:41","level":"INFO","message":"User logged in","session_id":"71ceabc51e417665eb86ff79c1fb3e08","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":123,"user_type":"admin","session_id":"71ceabc51e417665eb86ff79c1fb3e08","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:13:41","level":"ERROR","message":"Failed to log audit to database","session_id":"71ceabc51e417665eb86ff79c1fb3e08","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:13:41","user_id":123,"user_type":"admin","action_type":"ADMIN","action_description":"Test admin action","resource_type":"user","resource_id":"123","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"71ceabc51e417665eb86ff79c1fb3e08","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"test\":true}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:13:41","level":"ERROR","message":"Failed to log audit to database","session_id":"71ceabc51e417665eb86ff79c1fb3e08","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:13:41","user_id":123,"user_type":"admin","action_type":"LOGIN","action_description":"User logged in successfully","resource_type":"user","resource_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"71ceabc51e417665eb86ff79c1fb3e08","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"success\":true,\"user_type\":\"admin\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:13:41","level":"ERROR","message":"Failed to log audit to database","session_id":"71ceabc51e417665eb86ff79c1fb3e08","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:13:41","user_id":123,"user_type":"admin","action_type":"ADMIN","action_description":"Test admin action description","resource_type":null,"resource_id":null,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"71ceabc51e417665eb86ff79c1fb3e08","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"admin_action\":\"test\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:13:41","level":"ERROR","message":"Failed to log audit to database","session_id":"71ceabc51e417665eb86ff79c1fb3e08","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:13:41","user_id":123,"user_type":"admin","action_type":"SECURITY","action_description":"Test security event","resource_type":null,"resource_id":null,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"71ceabc51e417665eb86ff79c1fb3e08","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"security_event\":\"test_event\"}","severity":"WARNING"}}}
{"timestamp":"2025-06-02 17:13:41","level":"INFO","message":"User logged in","session_id":"16c8e47da534bd917fa613e45ddc5e90","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":999,"user_type":"admin","session_id":"16c8e47da534bd917fa613e45ddc5e90","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:13:41","level":"ERROR","message":"Failed to log audit to database","session_id":"16c8e47da534bd917fa613e45ddc5e90","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:13:41","user_id":999,"user_type":"admin","action_type":"FINANCIAL","action_description":"Test credit transaction","resource_type":"transaction","resource_id":"TEST123","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"16c8e47da534bd917fa613e45ddc5e90","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"action\":\"credit\",\"user_id\":999,\"amount\":250,\"currency\":\"USD\",\"transaction_id\":\"TEST123\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:13:41","level":"INFO","message":"Integration test completed","session_id":"16c8e47da534bd917fa613e45ddc5e90","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":999,"validated_data":{"email":"<EMAIL>","amount":250}}}
{"timestamp":"2025-06-02 17:16:34","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:16:34","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: smtp_test","resource_type":"email_test","resource_id":"smtp_test","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"smtp_test\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:16:45","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:16:45","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: otp","resource_type":"email_test","resource_id":"otp","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"otp\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:16:55","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:16:55","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: transaction","resource_type":"email_test","resource_id":"transaction","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"transaction\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:06","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:06","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: welcome","resource_type":"email_test","resource_id":"welcome","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"welcome\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:08","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:08","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: otp","resource_type":"email_test","resource_id":"otp","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"otp\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:10","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:10","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: password_reset","resource_type":"email_test","resource_id":"password_reset","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"password_reset\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:12","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:12","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: suspension","resource_type":"email_test","resource_id":"suspension","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"suspension\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:14","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:14","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: deletion","resource_type":"email_test","resource_id":"deletion","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"deletion\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:16","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:16","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: transaction","resource_type":"email_test","resource_id":"transaction","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"transaction\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:18","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:18","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: security","resource_type":"email_test","resource_id":"security","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"security\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:20","level":"ERROR","message":"Failed to log audit to database","session_id":"e04786079256ca3c3edcb94c4484b535","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-email-handler.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:20","user_id":null,"user_type":"anonymous","action_type":"ADMIN","action_description":"Email test sent: smtp_test","resource_type":"email_test","resource_id":"smtp_test","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"e04786079256ca3c3edcb94c4484b535","request_uri":"/online_banking/test/admin/test-email-handler.php","request_method":"POST","old_values":null,"new_values":null,"additional_data":"{\"recipient\":\"<EMAIL>\",\"success\":true,\"test_type\":\"smtp_test\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:42","level":"INFO","message":"Test error message","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"test":true}}
{"timestamp":"2025-06-02 17:17:42","level":"WARNING","message":"Input validation failed","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"errors":{"email":"Invalid email format","amount":"Must be at least 0","name":"This field is required"},"data_keys":["email","amount","name"]}}
{"timestamp":"2025-06-02 17:17:42","level":"DEBUG","message":"Session data set","session_id":"16c8e47da534bd917fa613e45ddc5e90","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":999}}
{"timestamp":"2025-06-02 17:17:42","level":"DEBUG","message":"Session data removed","session_id":"16c8e47da534bd917fa613e45ddc5e90","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"key":"test_key","user_id":999}}
{"timestamp":"2025-06-02 17:17:42","level":"INFO","message":"User logged in","session_id":"27702b0905bda8c9ccd4359096c1a081","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":123,"user_type":"admin","session_id":"27702b0905bda8c9ccd4359096c1a081","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:17:42","level":"ERROR","message":"Failed to log audit to database","session_id":"27702b0905bda8c9ccd4359096c1a081","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:42","user_id":123,"user_type":"admin","action_type":"ADMIN","action_description":"Test admin action","resource_type":"user","resource_id":"123","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"27702b0905bda8c9ccd4359096c1a081","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"test\":true}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:42","level":"ERROR","message":"Failed to log audit to database","session_id":"27702b0905bda8c9ccd4359096c1a081","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:42","user_id":123,"user_type":"admin","action_type":"LOGIN","action_description":"User logged in successfully","resource_type":"user","resource_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"27702b0905bda8c9ccd4359096c1a081","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"success\":true,\"user_type\":\"admin\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:42","level":"ERROR","message":"Failed to log audit to database","session_id":"27702b0905bda8c9ccd4359096c1a081","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:42","user_id":123,"user_type":"admin","action_type":"ADMIN","action_description":"Test admin action description","resource_type":null,"resource_id":null,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"27702b0905bda8c9ccd4359096c1a081","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"admin_action\":\"test\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:42","level":"ERROR","message":"Failed to log audit to database","session_id":"27702b0905bda8c9ccd4359096c1a081","user_id":123,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:42","user_id":123,"user_type":"admin","action_type":"SECURITY","action_description":"Test security event","resource_type":null,"resource_id":null,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"27702b0905bda8c9ccd4359096c1a081","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"security_event\":\"test_event\"}","severity":"WARNING"}}}
{"timestamp":"2025-06-02 17:17:42","level":"INFO","message":"User logged in","session_id":"b9e6b612605767dd986926ccbd16cfe1","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":999,"user_type":"admin","session_id":"b9e6b612605767dd986926ccbd16cfe1","ip_address":"::1"}}
{"timestamp":"2025-06-02 17:17:42","level":"ERROR","message":"Failed to log audit to database","session_id":"b9e6b612605767dd986926ccbd16cfe1","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"error":"Unknown column 'timestamp' in 'field list'","audit_data":{"timestamp":"2025-06-02 17:17:42","user_id":999,"user_type":"admin","action_type":"FINANCIAL","action_description":"Test credit transaction","resource_type":"transaction","resource_id":"TEST123","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","session_id":"b9e6b612605767dd986926ccbd16cfe1","request_uri":"/online_banking/test/admin/test-phase2-security.php","request_method":"GET","old_values":null,"new_values":null,"additional_data":"{\"action\":\"credit\",\"user_id\":999,\"amount\":250,\"currency\":\"USD\",\"transaction_id\":\"TEST123\"}","severity":"INFO"}}}
{"timestamp":"2025-06-02 17:17:42","level":"INFO","message":"Integration test completed","session_id":"b9e6b612605767dd986926ccbd16cfe1","user_id":999,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/admin/test-phase2-security.php","context":{"user_id":999,"validated_data":{"email":"<EMAIL>","amount":250}}}
[2025-06-04 09:27:58] NOTICE: session_start(): Ignoring session_start() because a session is already active (started from C:\MAMP\htdocs\online_banking\config\config.php on line 8) in C:\MAMP\htdocs\online_banking\banking_email_test.php:13
User: guest (IP: ::1)
Request: GET /online_banking/banking_email_test.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\banking_email_test.php(13): session_start()
---
[2025-06-04 09:31:30] WARNING: mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\MAMP\htdocs\online_banking\test_email_to_demothedev.php:72
User: guest (IP: ::1)
Request: GET /online_banking/test_email_to_demothedev.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_email_to_demothedev.php(72): mail()
---
[2025-06-04 09:31:36] WARNING: mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\MAMP\htdocs\online_banking\test_email_to_demothedev.php:292
User: guest (IP: ::1)
Request: GET /online_banking/test_email_to_demothedev.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_email_to_demothedev.php(292): mail()
---
[2025-06-04 09:54:05] WARNING: mail(): SMTP server response: 554 5.7.1 &lt;unknown[***************]&gt;: Client host rejected: Access denied in C:\MAMP\htdocs\online_banking\config\email.php:99
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\config\email.php(99): mail()
#3 C:\MAMP\htdocs\online_banking\test_smtp_connection.php(59): sendEmailSMTP()
---
[2025-06-04 09:59:51] WARNING: require(C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): require()
#3 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(45): {closure}()
#4 C:\MAMP\htdocs\online_banking\vendor\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()
#5 C:\MAMP\htdocs\online_banking\config\email.php(88): require_once()
#6 C:\MAMP\htdocs\online_banking\test_smtp_connection.php(59): sendEmailSMTP()
---
[2025-06-04 09:59:51] EXCEPTION: Failed opening required 'C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\vendor\\composer\\autoload_real.php(45): {closure}('992df191b496611...', 'C:\\\\MAMP\\\\htdocs\\\\...')\n#1 C:\\MAMP\\htdocs\\online_banking\\vendor\\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()\n#2 C:\\MAMP\\htdocs\\online_banking\\config\\email.php(88): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#3 C:\\MAMP\\htdocs\\online_banking\\test_smtp_connection.php(59): sendEmailSMTP('demohome4041@gm...', 'SMTP Connection...', 'This is a test ...')\n#4 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:00:01] WARNING: require(C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): require()
#3 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(45): {closure}()
#4 C:\MAMP\htdocs\online_banking\vendor\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()
#5 C:\MAMP\htdocs\online_banking\config\email.php(88): require_once()
#6 C:\MAMP\htdocs\online_banking\test_smtp_connection.php(59): sendEmailSMTP()
---
[2025-06-04 10:00:01] EXCEPTION: Failed opening required 'C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\vendor\\composer\\autoload_real.php(45): {closure}('992df191b496611...', 'C:\\\\MAMP\\\\htdocs\\\\...')\n#1 C:\\MAMP\\htdocs\\online_banking\\vendor\\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()\n#2 C:\\MAMP\\htdocs\\online_banking\\config\\email.php(88): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#3 C:\\MAMP\\htdocs\\online_banking\\test_smtp_connection.php(59): sendEmailSMTP('demohome4041@gm...', 'SMTP Connection...', 'This is a test ...')\n#4 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:02:30] WARNING: require(C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): require()
#3 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(45): {closure}()
#4 C:\MAMP\htdocs\online_banking\vendor\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()
#5 C:\MAMP\htdocs\online_banking\config\email.php(88): require_once()
#6 C:\MAMP\htdocs\online_banking\test_smtp_connection.php(59): sendEmailSMTP()
---
[2025-06-04 10:02:30] EXCEPTION: Failed opening required 'C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\vendor\\composer\\autoload_real.php(45): {closure}('992df191b496611...', 'C:\\\\MAMP\\\\htdocs\\\\...')\n#1 C:\\MAMP\\htdocs\\online_banking\\vendor\\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()\n#2 C:\\MAMP\\htdocs\\online_banking\\config\\email.php(88): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#3 C:\\MAMP\\htdocs\\online_banking\\test_smtp_connection.php(59): sendEmailSMTP('demohome4041@gm...', 'SMTP Connection...', 'This is a test ...')\n#4 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:09:09] WARNING: require(C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): require()
#3 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(45): {closure}()
#4 C:\MAMP\htdocs\online_banking\vendor\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()
#5 C:\MAMP\htdocs\online_banking\config\email.php(88): require_once()
#6 C:\MAMP\htdocs\online_banking\test_smtp_connection.php(59): sendEmailSMTP()
---
[2025-06-04 10:09:09] EXCEPTION: Failed opening required 'C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\vendor\\composer\\autoload_real.php(45): {closure}('992df191b496611...', 'C:\\\\MAMP\\\\htdocs\\\\...')\n#1 C:\\MAMP\\htdocs\\online_banking\\vendor\\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()\n#2 C:\\MAMP\\htdocs\\online_banking\\config\\email.php(88): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#3 C:\\MAMP\\htdocs\\online_banking\\test_smtp_connection.php(59): sendEmailSMTP('demothedev@gmai...', 'SMTP Connection...', 'This is a test ...')\n#4 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:12:41] WARNING: mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in C:\MAMP\htdocs\online_banking\test_email_to_demothedev.php:72
User: guest (IP: ::1)
Request: GET /online_banking/test_email_to_demothedev.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_email_to_demothedev.php(72): mail()
---
[2025-06-04 10:12:41] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\config\email.php:21
User: guest (IP: ::1)
Request: GET /online_banking/test_email_to_demothedev.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\test_email_to_demothedev.php(110): sendEmail('demothedev@gmai...', '\\xF0\\x9F\\x8F\\xA6 Banking Sy...', '\\n    <html>\\n   ...', true)\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:13:31] WARNING: require(C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/phpmailer_test_demothedev.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): require()
#3 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(45): {closure}()
#4 C:\MAMP\htdocs\online_banking\vendor\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()
#5 C:\MAMP\htdocs\online_banking\phpmailer_test_demothedev.php(57): require_once()
---
[2025-06-04 10:13:31] EXCEPTION: Failed opening required 'C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/phpmailer_test_demothedev.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\vendor\\composer\\autoload_real.php(45): {closure}('992df191b496611...', 'C:\\\\MAMP\\\\htdocs\\\\...')\n#1 C:\\MAMP\\htdocs\\online_banking\\vendor\\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()\n#2 C:\\MAMP\\htdocs\\online_banking\\phpmailer_test_demothedev.php(57): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#3 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:13:51] WARNING: require(C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): require()
#3 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(45): {closure}()
#4 C:\MAMP\htdocs\online_banking\vendor\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()
#5 C:\MAMP\htdocs\online_banking\config\email.php(88): require_once()
#6 C:\MAMP\htdocs\online_banking\test_smtp_connection.php(59): sendEmailSMTP()
---
[2025-06-04 10:13:51] EXCEPTION: Failed opening required 'C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: guest (IP: ::1)
Request: GET /online_banking/test_smtp_connection.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\vendor\\composer\\autoload_real.php(45): {closure}('992df191b496611...', 'C:\\\\MAMP\\\\htdocs\\\\...')\n#1 C:\\MAMP\\htdocs\\online_banking\\vendor\\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()\n#2 C:\\MAMP\\htdocs\\online_banking\\config\\email.php(88): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#3 C:\\MAMP\\htdocs\\online_banking\\test_smtp_connection.php(59): sendEmailSMTP('demothedev@gmai...', 'SMTP Connection...', 'This is a test ...')\n#4 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:29:28] WARNING: require_once(includes/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\test_user_creation_with_email.php:11
User: 1 (IP: ::1)
Request: GET /online_banking/test_user_creation_with_email.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\test_user_creation_with_email.php(11): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_user_creation_with_email.php(11): require_once()
---
[2025-06-04 10:29:28] EXCEPTION: Failed opening required 'includes/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\test_user_creation_with_email.php:11
User: 1 (IP: ::1)
Request: GET /online_banking/test_user_creation_with_email.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:30:25] WARNING: require_once(includes/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin_add_user_test.php:19
User: 1 (IP: ::1)
Request: GET /online_banking/admin_add_user_test.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin_add_user_test.php(19): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin_add_user_test.php(19): require_once()
---
[2025-06-04 10:30:25] EXCEPTION: Failed opening required 'includes/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin_add_user_test.php:19
User: 1 (IP: ::1)
Request: GET /online_banking/admin_add_user_test.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:30:29] WARNING: require_once(includes/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin_add_user_test.php:19
User: 1 (IP: ::1)
Request: GET /online_banking/admin_add_user_test.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin_add_user_test.php(19): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin_add_user_test.php(19): require_once()
---
[2025-06-04 10:30:29] EXCEPTION: Failed opening required 'includes/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin_add_user_test.php:19
User: 1 (IP: ::1)
Request: GET /online_banking/admin_add_user_test.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:36:27] WARNING: require_once(includes/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\test_user_creation_simple.php:16
User: 1 (IP: ::1)
Request: GET /online_banking/test_user_creation_simple.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\test_user_creation_simple.php(16): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_user_creation_simple.php(16): require_once()
---
[2025-06-04 10:36:27] EXCEPTION: Failed opening required 'includes/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\test_user_creation_simple.php:16
User: 1 (IP: ::1)
Request: GET /online_banking/test_user_creation_simple.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 10:57:27] WARNING: require_once(includes/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\test_user_creation_simple.php:16
User: guest (IP: ::1)
Request: GET /online_banking/test_user_creation_simple.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\test_user_creation_simple.php(16): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_user_creation_simple.php(16): require_once()
---
[2025-06-04 10:57:27] EXCEPTION: Failed opening required 'includes/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\test_user_creation_simple.php:16
User: guest (IP: ::1)
Request: GET /online_banking/test_user_creation_simple.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 11:27:05] EXCEPTION: syntax error, unexpected token "=" in C:\MAMP\htdocs\online_banking\config\super_admin_settings.php:146
User: guest (IP: ::1)
Request: GET /online_banking/test_email_templates.php
Context: {"exception_class":"ParseError","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\config\\config.php(59): require_once()\n#1 C:\\MAMP\\htdocs\\online_banking\\test_email_templates.php(12): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#2 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 11:29:13] EXCEPTION: syntax error, unexpected token "=" in C:\MAMP\htdocs\online_banking\config\super_admin_settings.php:146
User: guest (IP: ::1)
Request: GET /online_banking/test_improved_templates.php
Context: {"exception_class":"ParseError","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\config\\config.php(59): require_once()\n#1 C:\\MAMP\\htdocs\\online_banking\\test_improved_templates.php(32): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#2 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-04 11:33:10] WARNING: Undefined array key "username" in C:\MAMP\htdocs\online_banking\config\email_templates.php:251
User: guest (IP: ::1)
Request: GET /online_banking/simple_improvements_test.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\config\email_templates.php(251): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\simple_improvements_test.php(49): generateWelcomeEmailTemplate()
---
[2025-06-04 11:34:52] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: GET /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:34:52] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: GET /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:35:30] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:35:30] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:16] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:16] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:17] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:17] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:17] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:17] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:17] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:17] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:18] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:18] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:18] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:18] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:18] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:18] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:19] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:19] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: POST /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:23] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: GET /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 11:49:23] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php:297
User: 1 (IP: ::1)
Request: GET /online_banking/admin/super-admin-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\super-admin-settings.php(297): include()
---
[2025-06-04 22:43:47] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\login.php:73
User: guest (IP: ::1)
Request: POST /online_banking/login.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:29:56] WARNING: require_once(config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\register.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\register.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\register.php(4): require_once()
---
[2025-06-05 06:29:56] EXCEPTION: Failed opening required 'config/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\register.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:30:04] WARNING: require_once(../config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(4): require_once()
---
[2025-06-05 06:30:04] EXCEPTION: Failed opening required '../config/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:30:40] WARNING: require_once(../config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(4): require_once()
---
[2025-06-05 06:30:40] EXCEPTION: Failed opening required '../config/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:30:42] WARNING: require_once(config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\test_registration_system.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/test_registration_system.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\test_registration_system.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_registration_system.php(4): require_once()
---
[2025-06-05 06:30:42] EXCEPTION: Failed opening required 'config/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\test_registration_system.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/test_registration_system.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:31:00] WARNING: require_once(../config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(4): require_once()
---
[2025-06-05 06:31:00] EXCEPTION: Failed opening required '../config/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:31:57] WARNING: require_once(config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\register.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\register.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\register.php(4): require_once()
---
[2025-06-05 06:31:57] EXCEPTION: Failed opening required 'config/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\register.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:33:25] WARNING: require_once(config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\register.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/debug_pages.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\register.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\register.php(4): require_once()
#3 C:\MAMP\htdocs\online_banking\debug_pages.php(81): include()
---
[2025-06-05 06:37:07] WARNING: require_once(config/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\test_registration_system.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/test_registration_system.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\test_registration_system.php(4): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_registration_system.php(4): require_once()
---
[2025-06-05 06:37:07] EXCEPTION: Failed opening required 'config/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\test_registration_system.php:4
User: 1 (IP: ::1)
Request: GET /online_banking/test_registration_system.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:37:20] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:221
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): include()
---
[2025-06-05 06:37:20] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:221
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): include()
---
[2025-06-05 06:37:37] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:221
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): include()
---
[2025-06-05 06:37:37] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:221
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): include()
---
[2025-06-05 06:37:43] WARNING: include(../includes/admin_footer.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:221
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): include()
---
[2025-06-05 06:37:43] WARNING: include(): Failed opening '../includes/admin_footer.php' for inclusion (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:221
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(221): include()
---
[2025-06-05 06:54:34] WARNING: Undefined variable $pending_accounts in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:215
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(215): bankingErrorHandler()
---
[2025-06-05 06:54:34] EXCEPTION: count(): Argument #1 ($value) must be of type Countable|array, null given in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:215
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: {"exception_class":"TypeError","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:54:41] WARNING: Undefined variable $pending_accounts in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:215
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(215): bankingErrorHandler()
---
[2025-06-05 06:54:41] EXCEPTION: count(): Argument #1 ($value) must be of type Countable|array, null given in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:215
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: {"exception_class":"TypeError","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 06:54:55] WARNING: Undefined variable $pending_accounts in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:215
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-accounts.php(215): bankingErrorHandler()
---
[2025-06-05 06:54:55] EXCEPTION: count(): Argument #1 ($value) must be of type Countable|array, null given in C:\MAMP\htdocs\online_banking\admin\pending-accounts.php:215
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-accounts.php
Context: {"exception_class":"TypeError","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
{"timestamp":"2025-06-05 07:13:23","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:133","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:13:30","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:133","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:18:07","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:18:08","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:18:32","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:18:33","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:18:43","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:18:44","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:23:18","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:23:19","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
{"timestamp":"2025-06-05 07:23:20","level":"ERROR","message":"Exception: Class \"SessionManager\" not found in C:\\MAMP\\htdocs\\online_banking\\register.php:165","session_id":"1440b639fd6ce4e1f45ccc31a56abc00","user_id":1,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/register.php","context":{"uncaught":true,"exception_class":"Error","stack_trace":"#0 {main}"}}
[2025-06-05 07:57:15] WARNING: Undefined variable $errors in C:\MAMP\htdocs\online_banking\register.php:450
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\register.php(450): bankingErrorHandler()
---
[2025-06-05 07:57:15] WARNING: Undefined variable $csrf_token in C:\MAMP\htdocs\online_banking\register.php:466
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\register.php(466): bankingErrorHandler()
---
[2025-06-05 07:57:15] DEPRECATED: htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in C:\MAMP\htdocs\online_banking\register.php:466
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\register.php(466): htmlspecialchars()
---
[2025-06-05 08:04:57] WARNING: Undefined variable $errors in C:\MAMP\htdocs\online_banking\register.php:151
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\register.php(151): bankingErrorHandler()
---
[2025-06-05 08:04:57] WARNING: Undefined variable $csrf_token in C:\MAMP\htdocs\online_banking\register.php:167
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\register.php(167): bankingErrorHandler()
---
[2025-06-05 08:04:57] DEPRECATED: htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in C:\MAMP\htdocs\online_banking\register.php:167
User: 1 (IP: ::1)
Request: GET /online_banking/register.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\register.php(167): htmlspecialchars()
---
[2025-06-05 08:09:31] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\config\email.php:301
User: guest (IP: ::1)
Request: POST /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\register.php(71): sendPendingApprovalEmail('john.doe@exampl...', Array)\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 08:20:35] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\config\email.php:301
User: guest (IP: ::1)
Request: POST /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\register.php(115): sendPendingApprovalEmail('newuser2024@exa...', Array)\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 08:25:41] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\config\email.php:301
User: guest (IP: ::1)
Request: POST /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\register.php(120): sendPendingApprovalEmail('uniqueuser2024@...', Array)\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 08:28:09] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\config\email.php:301
User: guest (IP: ::1)
Request: POST /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\register.php(121): sendPendingApprovalEmail('michael.johnson...', Array)\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 08:43:35] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\config\email.php:301
User: guest (IP: ::1)
Request: POST /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\register.php(121): sendPendingApprovalEmail('emma.davis@test...', Array)\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:02:03] EXCEPTION: Call to undefined function isValidEmail() in C:\MAMP\htdocs\online_banking\config\email.php:301
User: guest (IP: ::1)
Request: POST /online_banking/register.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\register.php(121): sendPendingApprovalEmail('demohomexx@gmai...', Array)\n#1 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:19:03] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 09:19:03] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 09:19:03] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:21:35] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 09:21:35] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 09:21:35] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:21:59] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: POST /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:22:19] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php?resend=1
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:22:59] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: POST /online_banking/admin/verify-otp.php?resend=1
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:23:41] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: POST /online_banking/admin/verify-otp.php?resend=1
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:24:34] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php?resend=1
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:24:58] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: POST /online_banking/admin/verify-otp.php?resend=1
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:32:14] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 09:32:14] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 09:32:14] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:34:25] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:35:12] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 09:35:12] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 09:35:12] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:35:21] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:35:36] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php?resend=1
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:38:18] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:544
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php?resend=1
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:40:36] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 09:40:36] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 09:40:36] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:556
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:42:42] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:556
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:45:00] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 09:45:00] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 09:45:00] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:556
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:52:53] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 09:52:53] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 09:52:53] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:566
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:53:28] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:566
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:53:29] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:566
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:53:29] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:566
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:53:30] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:566
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 09:53:30] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:566
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 10:03:45] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 10:03:45] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 10:03:45] EXCEPTION: Call to undefined function maskEmail() in C:\MAMP\htdocs\online_banking\admin\verify-otp.php:568
User: guest (IP: ::1)
Request: GET /online_banking/admin/verify-otp.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 10:05:26] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:82
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(82): bankingErrorHandler()
---
[2025-06-05 10:05:26] WARNING: Undefined array key "email" in C:\MAMP\htdocs\online_banking\admin\login.php:86
User: guest (IP: ::1)
Request: POST /online_banking/admin/login.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\login.php(86): bankingErrorHandler()
---
[2025-06-05 10:17:38] WARNING: require(C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: 1 (IP: ::1)
Request: GET /online_banking/admin/email-settings.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(41): require()
#3 C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php(45): {closure}()
#4 C:\MAMP\htdocs\online_banking\vendor\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()
#5 C:\MAMP\htdocs\online_banking\admin\email-settings.php(12): require_once()
---
[2025-06-05 10:17:38] EXCEPTION: Failed opening required 'C:\MAMP\htdocs\online_banking\vendor\composer/../../config.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\vendor\composer\autoload_real.php:41
User: 1 (IP: ::1)
Request: GET /online_banking/admin/email-settings.php
Context: {"exception_class":"Error","trace":"#0 C:\\MAMP\\htdocs\\online_banking\\vendor\\composer\\autoload_real.php(45): {closure}('992df191b496611...', 'C:\\\\MAMP\\\\htdocs\\\\...')\n#1 C:\\MAMP\\htdocs\\online_banking\\vendor\\autoload.php(25): ComposerAutoloaderInit2185d2f99bcd56787481d9357a5972d3::getLoader()\n#2 C:\\MAMP\\htdocs\\online_banking\\admin\\email-settings.php(12): require_once('C:\\\\MAMP\\\\htdocs\\\\...')\n#3 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:13] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:27:30] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 11:28:46] WARNING: Undefined array key "email_verified" in C:\MAMP\htdocs\online_banking\admin\pending-users.php:231
User: 1 (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\pending-users.php(231): bankingErrorHandler()
---
[2025-06-05 18:45:40] WARNING: Undefined array key "first_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:846
User: guest (IP: ::1)
Request: GET /online_banking/admin/transactions.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(846): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\transactions.php(108): include()
---
[2025-06-05 18:45:40] WARNING: Undefined array key "last_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:846
User: guest (IP: ::1)
Request: GET /online_banking/admin/transactions.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(846): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\transactions.php(108): include()
---
[2025-06-05 18:45:40] WARNING: Undefined array key "first_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/transactions.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\transactions.php(108): include()
---
[2025-06-05 18:45:40] DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/transactions.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): substr()
#3 C:\MAMP\htdocs\online_banking\admin\transactions.php(108): include()
---
[2025-06-05 18:45:40] WARNING: Undefined array key "last_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/transactions.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\transactions.php(108): include()
---
[2025-06-05 18:45:40] DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/transactions.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): substr()
#3 C:\MAMP\htdocs\online_banking\admin\transactions.php(108): include()
---
[2025-06-05 18:46:26] WARNING: Undefined array key "first_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:846
User: guest (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(846): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-users.php(79): include()
---
[2025-06-05 18:46:26] WARNING: Undefined array key "last_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:846
User: guest (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(846): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-users.php(79): include()
---
[2025-06-05 18:46:26] WARNING: Undefined array key "first_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-users.php(79): include()
---
[2025-06-05 18:46:26] DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): substr()
#3 C:\MAMP\htdocs\online_banking\admin\pending-users.php(79): include()
---
[2025-06-05 18:46:26] WARNING: Undefined array key "last_name" in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\pending-users.php(79): include()
---
[2025-06-05 18:46:26] DEPRECATED: substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php:850
User: guest (IP: ::1)
Request: GET /online_banking/admin/pending-users.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\admin\includes\admin-header.php(850): substr()
#3 C:\MAMP\htdocs\online_banking\admin\pending-users.php(79): include()
---
[2025-06-17 13:38:51] WARNING: Undefined array key "username" in C:\MAMP\htdocs\online_banking\config\email_templates.php:301
User: guest (IP: ::1)
Request: GET /online_banking/simple_improvements_test.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\config\email_templates.php(301): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\simple_improvements_test.php(49): generateWelcomeEmailTemplate()
---
[2025-06-17 13:50:20] WARNING: Undefined array key "username" in C:\MAMP\htdocs\online_banking\config\email_templates.php:319
User: 1 (IP: ::1)
Request: GET /online_banking/simple_improvements_test.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\config\email_templates.php(319): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\simple_improvements_test.php(49): generateWelcomeEmailTemplate()
---
[2025-06-17 21:13:48] WARNING: require_once(includes/functions.php): Failed to open stream: No such file or directory in C:\MAMP\htdocs\online_banking\test_user_creation_simple.php:16
User: guest (IP: ::1)
Request: GET /online_banking/test_user_creation_simple.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\test_user_creation_simple.php(16): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\test_user_creation_simple.php(16): require_once()
---
[2025-06-17 21:13:48] EXCEPTION: Failed opening required 'includes/functions.php' (include_path='.;C:\php\pear') in C:\MAMP\htdocs\online_banking\test_user_creation_simple.php:16
User: guest (IP: ::1)
Request: GET /online_banking/test_user_creation_simple.php
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-21 16:07:31] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:126
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(126): number_format()
---
[2025-06-21 16:07:31] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:130
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(130): number_format()
---
[2025-06-21 16:07:31] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:144
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(144): number_format()
---
[2025-06-21 16:07:31] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:149
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(149): number_format()
---
[2025-06-21 16:47:47] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:126
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(126): number_format()
---
[2025-06-21 16:47:47] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:130
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(130): number_format()
---
[2025-06-21 16:47:47] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:144
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(144): number_format()
---
[2025-06-21 16:47:47] DEPRECATED: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\MAMP\htdocs\online_banking\dashboard\payments.php:149
User: 16 (IP: ::1)
Request: GET /online_banking/dashboard/payments.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\dashboard\payments.php(149): number_format()
---
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"ini_set(): Session ini settings cannot be changed when a session is active","session_id":"36de2655470693f3412330de031ce1c2","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":35,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"ini_set(): Session ini settings cannot be changed when a session is active","session_id":"36de2655470693f3412330de031ce1c2","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":36,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"ini_set(): Session ini settings cannot be changed when a session is active","session_id":"36de2655470693f3412330de031ce1c2","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":37,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"ini_set(): Session ini settings cannot be changed when a session is active","session_id":"36de2655470693f3412330de031ce1c2","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":38,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"ini_set(): Session ini settings cannot be changed when a session is active","session_id":"36de2655470693f3412330de031ce1c2","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":41,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"session_name(): Session name cannot be changed when a session is active","session_id":"36de2655470693f3412330de031ce1c2","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":44,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"session_regenerate_id(): Session ID cannot be regenerated when there is no active session","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":185,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"INFO","message":"User logged in","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":18,"user_type":"user","session_id":"","ip_address":"::1"}}
{"timestamp":"2025-06-24 13:19:15","level":"DEBUG","message":"Session extended","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":18}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"session_regenerate_id(): Session ID cannot be regenerated when there is no active session","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\test\\user\\comprehensive_session_test.php","line":224,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"DEBUG","message":"Session data set","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"test_key","user_id":18}}
{"timestamp":"2025-06-24 13:19:15","level":"DEBUG","message":"Session data removed","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"test_key","user_id":18}}
{"timestamp":"2025-06-24 13:19:15","level":"DEBUG","message":"Session data set","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"exists_key","user_id":18}}
{"timestamp":"2025-06-24 13:19:15","level":"INFO","message":"User logged out","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":18,"reason":"Test logout","session_duration":0}}
{"timestamp":"2025-06-24 13:19:15","level":"INFO","message":"Session destroyed","session_id":"no-session","user_id":18,"ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"reason":"Test logout","user_id":18,"session_id":""}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"session_destroy(): Trying to destroy uninitialized session","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":282,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"session_destroy(): Trying to destroy uninitialized session","session_id":"no-session","user_id":"anonymous","ip_address":"::1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\test\\user\\comprehensive_session_test.php","line":44,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"DEBUG","message":"strlen(): Passing null to parameter #1 ($string) of type string is deprecated","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":"anonymous","ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\test\\user\\session_security_test.php","line":120,"severity":8192}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"session_regenerate_id(): Session ID cannot be regenerated after headers have already been sent","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":"anonymous","ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":185,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"INFO","message":"User logged in","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":1,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":1,"user_type":"user","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","ip_address":"192.168.1.999"}}
{"timestamp":"2025-06-24 13:19:15","level":"WARNING","message":"session_regenerate_id(): Session ID cannot be regenerated after headers have already been sent","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":1,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":185,"severity":2}}
{"timestamp":"2025-06-24 13:19:15","level":"INFO","message":"User logged in","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":1,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":1,"user_type":"user","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","ip_address":"192.168.1.999"}}
{"timestamp":"2025-06-24 13:19:16","level":"DEBUG","message":"Session extended","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":1,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":1}}
{"timestamp":"2025-06-24 13:19:16","level":"WARNING","message":"session_regenerate_id(): Session ID cannot be regenerated after headers have already been sent","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":1,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":185,"severity":2}}
{"timestamp":"2025-06-24 13:19:16","level":"INFO","message":"User logged in","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":1,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":1,"user_type":"user","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","ip_address":"192.168.1.999"}}
{"timestamp":"2025-06-24 13:19:16","level":"DEBUG","message":"Session data set","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":1,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"test_data","user_id":1}}
{"timestamp":"2025-06-24 13:19:16","level":"DEBUG","message":"Session data set","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":123,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"user_id","user_id":123}}
{"timestamp":"2025-06-24 13:19:16","level":"DEBUG","message":"Session data set","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":123,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"username","user_id":123}}
{"timestamp":"2025-06-24 13:19:16","level":"DEBUG","message":"Session data set","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":123,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"balance","user_id":123}}
{"timestamp":"2025-06-24 13:19:16","level":"DEBUG","message":"Session data set","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":123,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"sensitive_data","user_id":123}}
{"timestamp":"2025-06-24 13:19:16","level":"DEBUG","message":"Session data set","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":123,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"key":"user_preferences","user_id":123}}
{"timestamp":"2025-06-24 13:19:16","level":"INFO","message":"User logged out","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":123,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"user_id":123,"reason":"Security test logout","session_duration":0}}
{"timestamp":"2025-06-24 13:19:16","level":"INFO","message":"Session destroyed","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":123,"ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"reason":"Security test logout","user_id":123,"session_id":"a6b4dcd837c0d5f8e194408d37dfacf3"}}
{"timestamp":"2025-06-24 13:19:16","level":"WARNING","message":"Cannot modify header information - headers already sent","session_id":"a6b4dcd837c0d5f8e194408d37dfacf3","user_id":"anonymous","ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\config\\SessionManager.php","line":275,"severity":2}}
{"timestamp":"2025-06-24 13:19:16","level":"WARNING","message":"session_start(): Session cannot be started after headers have already been sent","session_id":"no-session","user_id":"anonymous","ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\test\\user\\session_security_test.php","line":309,"severity":2}}
{"timestamp":"2025-06-24 13:19:16","level":"WARNING","message":"session_destroy(): Trying to destroy uninitialized session","session_id":"no-session","user_id":"anonymous","ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\test\\user\\session_security_test.php","line":37,"severity":2}}
{"timestamp":"2025-06-24 13:19:16","level":"WARNING","message":"session_start(): Session cannot be started after headers have already been sent","session_id":"no-session","user_id":"anonymous","ip_address":"192.168.1.999","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","request_uri":"/online_banking/test/user/run_all_session_tests.php","context":{"file":"C:\\MAMP\\htdocs\\online_banking\\test\\user\\session_security_test.php","line":38,"severity":2}}
[2025-06-25 11:27:37] NOTICE: session_start(): Ignoring session_start() because a session is already active (started from C:\MAMP\htdocs\online_banking\config\config.php on line 8) in C:\MAMP\htdocs\online_banking\debug_redirect.php:11
User: 5 (IP: ::1)
Request: GET /online_banking/debug_redirect.php
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 unknown(0): bankingErrorHandler()
#2 C:\MAMP\htdocs\online_banking\debug_redirect.php(11): session_start()
---
[2025-06-25 12:43:52] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 13:32:05] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 13:39:30] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 13:40:36] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 13:43:11] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 13:43:57] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 13:47:56] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 13:47:56] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 13:47:56] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 13:58:32] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 13:58:32] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 13:58:32] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:33] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:00:33] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:33] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:38] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:00:38] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:38] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:38] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:00:38] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:38] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:00:39] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:02:28] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:02:28] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:02:28] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:04:25] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:04:25] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:04:25] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:04:37] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:04:37] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:04:37] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:04:59] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:04:59] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:04:59] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:05:41] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:05:41] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:05:41] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:05:42] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:05:42] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:05:42] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:05:42] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:05:42] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:05:42] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:09:53] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:09:53] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:09:53] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:10:01] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:10:01] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:10:01] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:14:03] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:14:03] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:14:03] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:37] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:16:37] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:37] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:39] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:16:39] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:39] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:40] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:41] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:16:41] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:16:41] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:17:01] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:17:01] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:17:01] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:17:14] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:17:14] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:17:14] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:18:18] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:18:18] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:18:18] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:43] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:43] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:43] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:45] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "card_type" in C:\MAMP\htdocs\online_banking\dashboard\index.php:245
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(245): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "expiry_month" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:23:46] WARNING: Undefined array key "expiry_year" in C:\MAMP\htdocs\online_banking\dashboard\index.php:264
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: none
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(391): ErrorLogger->logError()
#1 C:\MAMP\htdocs\online_banking\dashboard\index.php(264): bankingErrorHandler()
---
[2025-06-25 14:25:55] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:05] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:06] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:06] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:06] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:07] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:07] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:07] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:45] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:46] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:46] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:47] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
[2025-06-25 14:27:47] EXCEPTION: Call to a member function fetch_assoc() on true in C:\MAMP\htdocs\online_banking\dashboard\index.php:80
User: 5 (IP: ::1)
Request: GET /online_banking/dashboard/
Context: {"exception_class":"Error","trace":"#0 {main}"}
Stack Trace: #0 C:\MAMP\htdocs\online_banking\config\ErrorLogger.php(409): ErrorLogger->logError()
#1 unknown(0): bankingExceptionHandler()
---
