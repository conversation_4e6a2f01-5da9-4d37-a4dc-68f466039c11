/* ===== TRANSFERS PAGE STYLES ===== */

/* Transfer Form Styles */
.transfer-form-container {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.transfer-form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);
}

.transfer-form-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.transfer-form-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
}

/* Form Layout */
.transfer-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section-title svg {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
}

/* Enhanced Form Controls */
.form-group {
    position: relative;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: white;
    transition: all 0.2s ease;
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    transform: translateY(-1px);
}

.form-control.is-valid {
    border-color: var(--accent-color);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.form-text.text-success {
    color: var(--accent-color);
}

.form-text.text-danger {
    color: var(--danger-color);
}

/* Amount Input Enhancement */
.amount-input-group {
    position: relative;
}

.amount-input-group .currency-symbol {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 600;
    color: var(--text-secondary);
    z-index: 2;
}

.amount-input-group .form-control {
    padding-left: 3rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* Beneficiary Selection */
.beneficiary-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.beneficiary-option {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.beneficiary-option:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.02);
}

.beneficiary-option.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.beneficiary-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.beneficiary-account {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', monospace;
}

/* Transfer Summary */
.transfer-summary {
    background: var(--background-light);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-top: 2rem;
}

.transfer-summary-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-row:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--text-primary);
}

.summary-label {
    color: var(--text-secondary);
}

.summary-value {
    color: var(--text-primary);
    font-weight: 500;
}

/* Action Buttons */
.transfer-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-light);
}

.btn-transfer {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 150px;
}

.btn-transfer:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-transfer:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-cancel {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background: var(--background-light);
    color: var(--text-primary);
}

/* Recent Transfers */
.recent-transfers {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.recent-transfers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.recent-transfers-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.transfer-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.transfer-item:last-child {
    border-bottom: none;
}

.transfer-item:hover {
    background: var(--background-light);
    margin: 0 -1rem;
    padding: 1rem;
    border-radius: var(--radius-lg);
}

.transfer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.transfer-details {
    flex: 1;
}

.transfer-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.transfer-account {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', monospace;
}

.transfer-amount {
    font-weight: 600;
    color: var(--text-primary);
    text-align: right;
}

.transfer-date {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-align: right;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .transfer-form {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .beneficiary-selector {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .transfer-form-container {
        padding: 1.5rem;
    }
    
    .transfer-form-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .transfer-actions {
        flex-direction: column;
    }
    
    .btn-transfer,
    .btn-cancel {
        width: 100%;
        justify-content: center;
    }
    
    .transfer-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .transfer-amount,
    .transfer-date {
        text-align: left;
    }
}
