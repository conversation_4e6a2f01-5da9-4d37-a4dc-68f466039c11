<?php
// Set page variables
$page_title = 'My Cards';
$additional_css = ['cards.css'];
$additional_js = ['cards.js'];

// Include header template
require_once '../templates/user/header.php';

// Include database connection
require_once '../config/config.php';
requireLogin();

// Get user's virtual cards and comprehensive data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user's virtual cards with enhanced details
    $cards_sql = "SELECT vc.*,
                         COUNT(vct.id) as transaction_count,
                         SUM(CASE WHEN vct.transaction_type = 'debit' THEN vct.amount ELSE 0 END) as total_spent,
                         MAX(vct.created_at) as last_transaction_date
                  FROM virtual_cards vc
                  LEFT JOIN virtual_card_transactions vct ON vc.id = vct.card_id
                  WHERE vc.user_id = ?
                  GROUP BY vc.id
                  ORDER BY vc.created_at DESC";
    $cards_result = $db->query($cards_sql, [$user_id]);
    $virtual_cards = [];
    while ($card = $cards_result->fetch_assoc()) {
        $virtual_cards[] = $card;
    }

    // Get comprehensive card statistics
    $stats_sql = "SELECT
                    COUNT(*) as total_cards,
                    SUM(current_balance) as total_balance,
                    SUM(spending_limit) as total_limit,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cards,
                    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_cards,
                    AVG(spending_limit) as avg_limit
                  FROM virtual_cards WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $card_stats = $stats_result->fetch_assoc();

    // Get recent card transactions
    $recent_transactions_sql = "SELECT vct.*, vc.card_number, vc.card_type
                               FROM virtual_card_transactions vct
                               JOIN virtual_cards vc ON vct.card_id = vc.id
                               WHERE vc.user_id = ?
                               ORDER BY vct.created_at DESC
                               LIMIT 10";
    $recent_transactions_result = $db->query($recent_transactions_sql, [$user_id]);
    $recent_card_transactions = [];
    while ($transaction = $recent_transactions_result->fetch_assoc()) {
        $recent_card_transactions[] = $transaction;
    }

    // Get monthly spending by card
    $monthly_spending_sql = "SELECT vc.id, vc.card_number, vc.card_type,
                                   SUM(CASE WHEN vct.transaction_type = 'debit' THEN vct.amount ELSE 0 END) as monthly_spent
                            FROM virtual_cards vc
                            LEFT JOIN virtual_card_transactions vct ON vc.id = vct.card_id
                                AND MONTH(vct.created_at) = MONTH(CURRENT_DATE())
                                AND YEAR(vct.created_at) = YEAR(CURRENT_DATE())
                            WHERE vc.user_id = ?
                            GROUP BY vc.id";
    $monthly_spending_result = $db->query($monthly_spending_sql, [$user_id]);
    $monthly_spending = [];
    while ($spending = $monthly_spending_result->fetch_assoc()) {
        $monthly_spending[$spending['id']] = $spending;
    }

} catch (Exception $e) {
    error_log("Cards page error: " . $e->getMessage());
    $virtual_cards = [];
    $card_stats = ['total_cards' => 0, 'total_balance' => 0, 'total_limit' => 0, 'active_cards' => 0, 'blocked_cards' => 0, 'avg_limit' => 0];
    $recent_card_transactions = [];
    $monthly_spending = [];
}

?>

<!-- Include Sidebar -->
<?php require_once '../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Modern Header Design - Admin Style -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="page-title mb-1" style="color: #1a1a1a; font-weight: 600;">My Cards</h2>
                    <div class="text-muted" style="font-size: 0.9rem;">Manage your virtual cards and spending</div>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge" style="background: #cce7ff; color: #004085; border: 1px solid #99d3ff; padding: 8px 16px; border-radius: 20px; font-weight: 500;">
                        <?php echo $card_stats['total_cards']; ?> cards active
                    </span>
                    <a href="../transactions/?filter=card" class="btn" style="background: #6c757d; border-color: #6c757d; color: white; border-radius: 8px; padding: 8px 16px; font-weight: 500; text-decoration: none; margin-right: 8px;">
                        <i class="fas fa-list me-1"></i>
                        Card Transactions
                    </a>
                    <button class="btn" style="background: #007bff; border-color: #007bff; color: white; border-radius: 8px; padding: 8px 16px; font-weight: 500;" onclick="showCreateCardModal()">
                        <i class="fas fa-plus me-1"></i>
                        Create New Card
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Card Statistics - Admin Style -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); margin-right: 16px;">
                            <i class="fas fa-credit-card" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                <?php echo number_format($card_stats['total_cards']); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">Total Cards</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500; margin-right: 4px;">
                                    <?php echo $card_stats['active_cards']; ?> Active
                                </span>
                                <?php if ($card_stats['blocked_cards'] > 0): ?>
                                <span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    <?php echo $card_stats['blocked_cards']; ?> Blocked
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); margin-right: 16px;">
                            <i class="fas fa-wallet" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                <?php echo formatCurrency($card_stats['total_balance'], $_SESSION['currency'] ?? 'USD'); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">Available Balance</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    Across all cards
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%); margin-right: 16px;">
                            <i class="fas fa-chart-bar" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                <?php echo formatCurrency($card_stats['total_limit'], $_SESSION['currency'] ?? 'USD'); ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">Total Spending Limit</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #e2d9f3; color: #5a2d91; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    Avg: <?php echo formatCurrency($card_stats['avg_limit'], $_SESSION['currency'] ?? 'USD'); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card" style="border: 1px solid #e9ecef; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.04);">
                <div class="card-body" style="padding: 20px;">
                    <div class="d-flex align-items-center">
                        <div class="admin-avatar" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); margin-right: 16px;">
                            <i class="fas fa-chart-line" style="font-size: 1rem;"></i>
                        </div>
                        <div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">
                                <?php
                                $total_monthly_spent = array_sum(array_column($monthly_spending, 'monthly_spent'));
                                echo formatCurrency($total_monthly_spent, $_SESSION['currency'] ?? 'USD');
                                ?>
                            </div>
                            <div style="color: #6b7280; font-size: 0.875rem;">This Month's Spending</div>
                            <div style="margin-top: 8px;">
                                <span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 0.75rem; font-weight: 500;">
                                    <?php echo count($recent_card_transactions); ?> transactions
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cards Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Virtual Cards List -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Your Virtual Cards</h3>
                    <button class="btn-outline" onclick="showCreateCardModal()">Add New Card</button>
                </div>
                <div class="card-body">
                    <?php if (!empty($virtual_cards)): ?>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">
                            <?php foreach ($virtual_cards as $card): ?>
                                <div class="virtual-card" style="position: relative;">
                                    <div class="card-brand" style="display: flex; justify-content: space-between; align-items: center;">
                                        <span><?php echo strtoupper($card['card_type']); ?></span>
                                        <span class="badge <?php echo $card['status'] === 'active' ? 'status-completed' : 'status-pending'; ?>">
                                            <?php echo ucfirst($card['status']); ?>
                                        </span>
                                    </div>
                                    <div style="margin-bottom: 1rem;">
                                        <div style="font-size: 0.875rem; opacity: 0.8;">
                                            <?php echo htmlspecialchars($card['card_holder_name']); ?>
                                        </div>
                                    </div>
                                    <div class="card-number" style="margin-bottom: 1rem;">
                                        <?php
                                        // Mask card number for security
                                        $masked_number = substr($card['card_number'], 0, 4) . ' **** **** ' . substr($card['card_number'], -4);
                                        echo $masked_number;
                                        ?>
                                    </div>
                                    <div class="card-details">
                                        <div>
                                            <div style="font-size: 0.75rem; opacity: 0.8;">Exp</div>
                                            <div><?php echo sprintf('%02d/%02d', $card['expiry_month'], $card['expiry_year']); ?></div>
                                        </div>
                                        <div>
                                            <div style="font-size: 0.75rem; opacity: 0.8;">CVV</div>
                                            <div>***</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 0.75rem; opacity: 0.8;">Balance</div>
                                            <div>$<?php echo number_format($card['current_balance'], 2); ?></div>
                                        </div>
                                    </div>

                                    <!-- Card Actions -->
                                    <div style="position: absolute; top: 10px; right: 10px;">
                                        <div class="dropdown">
                                            <button class="btn btn-sm" style="background: rgba(255,255,255,0.2); border: none; color: white;" data-bs-toggle="dropdown">
                                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"/>
                                                </svg>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="viewCardDetails(<?php echo $card['id']; ?>)">View Details</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="topUpCard(<?php echo $card['id']; ?>)">Top Up</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="freezeCard(<?php echo $card['id']; ?>)">
                                                    <?php echo $card['status'] === 'active' ? 'Freeze Card' : 'Unfreeze Card'; ?>
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCard(<?php echo $card['id']; ?>)">Delete Card</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">💳</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Virtual Cards</h3>
                            <p style="color: #9ca3af; margin-bottom: 2rem;">Create your first virtual card to start making secure online payments.</p>
                            <button class="btn-primary" onclick="showCreateCardModal()">Create Your First Card</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="showCreateCardModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                            Create New Card
                        </button>
                        <button class="btn-outline" onclick="showTopUpModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M1 3a1 1 0 011-1h12a1 1 0 011 1H1zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zM9 8a1 1 0 012 0v6a1 1 0 11-2 0V8z"/>
                                <path fill-rule="evenodd" d="M14.5 3a1 1 0 01-1 1H13v9a2 2 0 01-2 2H5a2 2 0 01-2-2V4h-.5a1 1 0 01-1-1V2a1 1 0 011-1H6a1 1 0 011-1h2a1 1 0 011 1h3.5a1 1 0 011 1v1zM4.118 4L4 4.059V13a1 1 0 001 1h6a1 1 0 001-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                            </svg>
                            Top Up Cards
                        </button>
                        <button class="btn-outline" onclick="downloadStatement()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                                <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                            </svg>
                            Download Statement
                        </button>
                    </div>
                </div>
            </div>

            <!-- Card Tips -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Security Tips</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Keep CVV Secret</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Never share your CVV code with anyone</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Monitor Transactions</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Check your card activity regularly</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Freeze When Needed</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Instantly freeze cards if suspicious activity</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Card Modal -->
<div class="modal fade" id="createCardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Virtual Card</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createCardForm">
                    <div class="mb-3">
                        <label class="form-label">Card Holder Name</label>
                        <input type="text" class="form-control" name="card_holder_name" value="<?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Card Type</label>
                        <select class="form-control" name="card_type" required>
                            <option value="visa">Visa</option>
                            <option value="mastercard">Mastercard</option>
                            <option value="amex">American Express</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Spending Limit</label>
                        <input type="number" class="form-control" name="spending_limit" min="100" max="10000" value="1000" required>
                        <small class="form-text text-muted">Minimum $100, Maximum $10,000</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createCard()">Create Card</button>
            </div>
        </div>
    </div>
</div>

<script>
function showCreateCardModal() {
    const modal = new bootstrap.Modal(document.getElementById('createCardModal'));
    modal.show();
}

function createCard() {
    const form = document.getElementById('createCardForm');
    const formData = new FormData(form);

    // Show loading state
    const createBtn = event.target;
    const originalText = createBtn.innerHTML;
    createBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating...';
    createBtn.disabled = true;

    fetch('ajax/create_card.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Virtual card created successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the card');
    })
    .finally(() => {
        createBtn.innerHTML = originalText;
        createBtn.disabled = false;
    });
}

function viewCardDetails(cardId) {
    // Implement card details view
    alert('Card details functionality coming soon!');
}

function topUpCard(cardId) {
    // Implement card top-up
    alert('Card top-up functionality coming soon!');
}

function freezeCard(cardId) {
    // Implement card freeze/unfreeze
    alert('Card freeze functionality coming soon!');
}

function deleteCard(cardId) {
    if (confirm('Are you sure you want to delete this card? This action cannot be undone.')) {
        // Implement card deletion
        alert('Card deletion functionality coming soon!');
    }
}

function showTopUpModal() {
    alert('Top-up modal coming soon!');
}

function downloadStatement() {
    alert('Statement download coming soon!');
}
</script>

<?php
// Include footer template
require_once '../templates/user/footer.php';
?>
