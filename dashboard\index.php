<?php
// Set page variables
$page_title = 'Dashboard';
$additional_css = ['dashboard-clean.css'];

// Include header template
require_once '../templates/user/header.php';

// Include database connection and check authentication
require_once '../config/config.php';
requireLogin();

// Get user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user account information
    $user_sql = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    // Get current balance
    $current_balance = $user['balance'] ?? 0;

    // Get recent transactions (last 5)
    $transactions_sql = "SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
    $transactions_result = $db->query($transactions_sql, [$user_id]);
    $recent_transactions = [];
    if ($transactions_result) {
        while ($row = $transactions_result->fetch_assoc()) {
            $recent_transactions[] = $row;
        }
    }

    // Get monthly stats
    $current_month = date('Y-m');
    $monthly_stats_sql = "SELECT
        SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as total_credits,
        SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as total_debits,
        COUNT(*) as transaction_count
        FROM transactions
        WHERE user_id = ? AND DATE_FORMAT(created_at, '%Y-%m') = ?";

    $stats_result = $db->query($monthly_stats_sql, [$user_id, $current_month]);
    $monthly_stats = $stats_result->fetch_assoc();

    $total_credits = $monthly_stats['total_credits'] ?? 0;
    $total_debits = $monthly_stats['total_debits'] ?? 0;
    $transaction_count = $monthly_stats['transaction_count'] ?? 0;
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $user = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'account_number' => ''];
    $current_balance = 0;
    $total_credits = $total_debits = $transaction_count = 0;
    $recent_transactions = [];
}

?>

<!-- Include Sidebar -->
<?php require_once '../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
            <!-- Welcome Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="h3 mb-1">Welcome back, <?php echo htmlspecialchars($user['first_name'] ?? 'User'); ?>!</h1>
                    <p class="text-muted">Here's your banking overview for today.</p>
                </div>
            </div>

            <!-- Account Balance -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="balance-card">
                        <div class="balance-label">Current Balance</div>
                        <div class="balance-amount">$<?php echo number_format($current_balance, 2); ?></div>
                        <small style="opacity: 0.8;">Account: <?php echo htmlspecialchars($user['account_number'] ?? ''); ?></small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-icon success">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="stats-value">$<?php echo number_format($total_credits, 2); ?></div>
                                <div class="stats-label">Credits This Month</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-icon warning">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <div class="stats-value">$<?php echo number_format($total_debits, 2); ?></div>
                                <div class="stats-label">Debits This Month</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="mb-3">Quick Actions</h5>
                    <div class="quick-actions">
                        <a href="transfers/" class="action-btn">
                            <div class="action-icon" style="background: rgba(37, 99, 235, 0.1); color: var(--primary-color);">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Transfer Money</div>
                                <small class="text-muted">Send money to others</small>
                            </div>
                        </a>
                        <a href="payments/" class="action-btn">
                            <div class="action-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Pay Bills</div>
                                <small class="text-muted">Pay your bills online</small>
                            </div>
                        </a>
                        <a href="cards/" class="action-btn">
                            <div class="action-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Manage Cards</div>
                                <small class="text-muted">View and manage cards</small>
                            </div>
                        </a>
                        <a href="statements/" class="action-btn">
                            <div class="action-icon" style="background: rgba(139, 69, 19, 0.1); color: #8b4513;">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Statements</div>
                                <small class="text-muted">Download statements</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">Recent Transactions</h5>
                                <a href="transactions/" class="btn btn-outline-primary btn-sm">View All</a>
                            </div>

                            <?php if (empty($recent_transactions)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-receipt text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                                    <p class="text-muted mt-3">No transactions yet</p>
                                    <a href="transfers/" class="btn btn-primary btn-sm">Make Your First Transfer</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-icon <?php echo $transaction['transaction_type'] === 'credit' ? 'bg-success' : 'bg-danger'; ?>" style="background-color: <?php echo $transaction['transaction_type'] === 'credit' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)'; ?>; color: <?php echo $transaction['transaction_type'] === 'credit' ? 'var(--success-color)' : 'var(--danger-color)'; ?>;">
                                            <i class="fas fa-<?php echo $transaction['transaction_type'] === 'credit' ? 'arrow-down' : 'arrow-up'; ?>"></i>
                                        </div>
                                        <div class="transaction-details">
                                            <div class="transaction-title"><?php echo htmlspecialchars($transaction['description'] ?? 'Transaction'); ?></div>
                                            <div class="transaction-date"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                        </div>
                                        <div class="transaction-amount <?php echo $transaction['transaction_type']; ?>">
                                            <?php echo $transaction['transaction_type'] === 'credit' ? '+' : '-'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
</div>

<?php
// Include footer template
require_once '../templates/user/footer.php';
?>


