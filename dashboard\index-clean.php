<?php
// Include database connection and check authentication first
require_once '../config/config.php';
requireLogin();

// Set page variables
$page_title = 'Dashboard';

// Get user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user account information
    $user_sql = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    // Get current balance
    $current_balance = $user['balance'] ?? 0;
    
    // Get recent transactions (last 5)
    $transactions_sql = "SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
    $transactions_result = $db->query($transactions_sql, [$user_id]);
    $recent_transactions = [];
    if ($transactions_result) {
        while ($row = $transactions_result->fetch_assoc()) {
            $recent_transactions[] = $row;
        }
    }
    
    // Get monthly stats
    $current_month = date('Y-m');
    $monthly_stats_sql = "SELECT 
        SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as total_credits,
        SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as total_debits,
        COUNT(*) as transaction_count
        FROM transactions 
        WHERE user_id = ? AND DATE_FORMAT(created_at, '%Y-%m') = ?";
    
    $stats_result = $db->query($monthly_stats_sql, [$user_id, $current_month]);
    $monthly_stats = $stats_result->fetch_assoc();
    
    $total_credits = $monthly_stats['total_credits'] ?? 0;
    $total_debits = $monthly_stats['total_debits'] ?? 0;
    $transaction_count = $monthly_stats['transaction_count'] ?? 0;

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $user = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'account_number' => ''];
    $current_balance = 0;
    $total_credits = $total_debits = $transaction_count = 0;
    $recent_transactions = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?> - PremierBank Pro</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/dashboard-clean.css">
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <div class="banking-sidebar">
            <!-- Header -->
            <div class="sidebar-header">
                <div class="bank-logo">
                    <div class="logo-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="bank-name">PremierBank Pro</div>
                </div>
                <div class="user-info">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($user['first_name'] ?? 'U', 0, 1)); ?>
                    </div>
                    <div class="user-details">
                        <h6><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h6>
                        <small><?php echo htmlspecialchars($user['account_number'] ?? ''); ?></small>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="index.php" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="accounts/" class="nav-link">
                        <i class="fas fa-wallet"></i>
                        <span>Accounts</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="transfers/" class="nav-link">
                        <i class="fas fa-exchange-alt"></i>
                        <span>Transfers</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="transactions/" class="nav-link">
                        <i class="fas fa-list"></i>
                        <span>Transactions</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="cards/" class="nav-link">
                        <i class="fas fa-credit-card"></i>
                        <span>Cards</span>
                    </a>
                </div>
            </nav>

            <nav class="nav-section">
                <div class="nav-section-title">Services</div>
                <div class="nav-item">
                    <a href="payments/" class="nav-link">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Payments</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="beneficiaries/" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Beneficiaries</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="statements/" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        <span>Statements</span>
                    </a>
                </div>
            </nav>

            <nav class="nav-section">
                <div class="nav-section-title">Account</div>
                <div class="nav-item">
                    <a href="security/" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="../auth/logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Welcome Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="h3 mb-1">Welcome back, <?php echo htmlspecialchars($user['first_name'] ?? 'User'); ?>!</h1>
                    <p class="text-muted">Here's your banking overview for today.</p>
                </div>
            </div>

            <!-- Account Balance -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="balance-card">
                        <div class="balance-label">Current Balance</div>
                        <div class="balance-amount">$<?php echo number_format($current_balance, 2); ?></div>
                        <small style="opacity: 0.8;">Account: <?php echo htmlspecialchars($user['account_number'] ?? ''); ?></small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-icon success">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="stats-value">$<?php echo number_format($total_credits, 2); ?></div>
                                <div class="stats-label">Credits This Month</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-icon warning">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <div class="stats-value">$<?php echo number_format($total_debits, 2); ?></div>
                                <div class="stats-label">Debits This Month</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="mb-3">Quick Actions</h5>
                    <div class="quick-actions">
                        <a href="transfers/" class="action-btn">
                            <div class="action-icon" style="background: rgba(37, 99, 235, 0.1); color: var(--primary-color);">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Transfer Money</div>
                                <small class="text-muted">Send money to others</small>
                            </div>
                        </a>
                        <a href="payments/" class="action-btn">
                            <div class="action-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Pay Bills</div>
                                <small class="text-muted">Pay your bills online</small>
                            </div>
                        </a>
                        <a href="cards/" class="action-btn">
                            <div class="action-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Manage Cards</div>
                                <small class="text-muted">View and manage cards</small>
                            </div>
                        </a>
                        <a href="statements/" class="action-btn">
                            <div class="action-icon" style="background: rgba(139, 69, 19, 0.1); color: #8b4513;">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div>
                                <div class="fw-semibold">Statements</div>
                                <small class="text-muted">Download statements</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">Recent Transactions</h5>
                                <a href="transactions/" class="btn btn-outline-primary btn-sm">View All</a>
                            </div>
                            
                            <?php if (empty($recent_transactions)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-receipt text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                                    <p class="text-muted mt-3">No transactions yet</p>
                                    <a href="transfers/" class="btn btn-primary btn-sm">Make Your First Transfer</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-icon <?php echo $transaction['transaction_type'] === 'credit' ? 'bg-success' : 'bg-danger'; ?>" style="background-color: <?php echo $transaction['transaction_type'] === 'credit' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)'; ?>; color: <?php echo $transaction['transaction_type'] === 'credit' ? 'var(--success-color)' : 'var(--danger-color)'; ?>;">
                                            <i class="fas fa-<?php echo $transaction['transaction_type'] === 'credit' ? 'arrow-down' : 'arrow-up'; ?>"></i>
                                        </div>
                                        <div class="transaction-details">
                                            <div class="transaction-title"><?php echo htmlspecialchars($transaction['description'] ?? 'Transaction'); ?></div>
                                            <div class="transaction-date"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                        </div>
                                        <div class="transaction-amount <?php echo $transaction['transaction_type']; ?>">
                                            <?php echo $transaction['transaction_type'] === 'credit' ? '+' : '-'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
