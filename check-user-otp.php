<?php
/**
 * Check current valid OTP codes for user login
 */

session_start();

// Include required files
require_once 'config/config.php';

echo "<h1>User OTP Code Checker</h1>";

try {
    $db = getDB();
    
    // Check if we have a user OTP session
    if (isset($_SESSION['otp_user_id'])) {
        $user_id = $_SESSION['otp_user_id'];
        echo "✅ User OTP session found for user ID: $user_id<br>";
        
        // Get user details
        $sql = "SELECT id, username, first_name, last_name, email FROM accounts WHERE id = ?";
        $result = $db->query($sql, [$user_id]);
        
        if ($result && $result->num_rows === 1) {
            $user = $result->fetch_assoc();
            echo "👤 User: {$user['first_name']} {$user['last_name']} ({$user['username']})<br>";
            echo "📧 Email: {$user['email']}<br>";
            
            // Check for valid OTP codes
            $sql = "SELECT otp_code, expires_at, used, created_at FROM user_otps 
                    WHERE user_id = ? AND expires_at > UTC_TIMESTAMP() AND used = 0 
                    ORDER BY created_at DESC";
            $result = $db->query($sql, [$user_id]);
            
            if ($result && $result->num_rows > 0) {
                echo "<h2>Valid OTP Codes</h2>";
                while ($otp = $result->fetch_assoc()) {
                    echo "<div style='background: #e8f5e8; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
                    echo "🔑 <strong>OTP Code: {$otp['otp_code']}</strong><br>";
                    echo "⏰ Expires: {$otp['expires_at']}<br>";
                    echo "📅 Created: {$otp['created_at']}<br>";
                    echo "</div>";
                }
            } else {
                echo "❌ No valid OTP codes found<br>";
                
                // Generate a new OTP
                echo "<h2>Generating New OTP</h2>";
                $new_otp = generateOTP();
                
                if (storeOTP($user_id, $new_otp, 10)) {
                    echo "✅ New OTP generated: <strong>$new_otp</strong><br>";
                    echo "⏰ Valid for 10 minutes<br>";
                    
                    // Try to send email
                    $emailSent = sendOTPEmail($user['email'], $new_otp, $user['first_name'] . ' ' . $user['last_name']);
                    if ($emailSent) {
                        echo "📧 OTP email sent successfully<br>";
                    } else {
                        echo "⚠️ Email sending failed, but OTP is valid: <strong>$new_otp</strong><br>";
                    }
                } else {
                    echo "❌ Failed to generate new OTP<br>";
                }
            }
            
            echo "<br><a href='auth/verify-otp.php'>Go to OTP Verification</a><br>";
            
        } else {
            echo "❌ User not found<br>";
        }
        
    } else {
        echo "❌ No user OTP session found. Please login first.<br>";
        echo "<a href='login.php'>Go to User Login</a><br>";
    }
    
    // Show all recent OTPs for debugging
    echo "<h2>All Recent OTPs (Last 24 hours)</h2>";
    $sql = "SELECT u.otp_code, u.expires_at, u.used, u.created_at, a.username, a.first_name, a.last_name
            FROM user_otps u 
            JOIN accounts a ON u.user_id = a.id 
            WHERE u.created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY u.created_at DESC 
            LIMIT 10";
    $result = $db->query($sql);
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>OTP Code</th><th>User</th><th>Expires</th><th>Used</th><th>Created</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $status = $row['used'] ? '✅ Used' : (strtotime($row['expires_at']) > time() ? '🟢 Valid' : '🔴 Expired');
            echo "<tr>";
            echo "<td><strong>{$row['otp_code']}</strong></td>";
            echo "<td>{$row['first_name']} {$row['last_name']} ({$row['username']})</td>";
            echo "<td>{$row['expires_at']}</td>";
            echo "<td>$status</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No recent OTPs found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<br><a href='login.php'>Back to Login</a>";
?>
