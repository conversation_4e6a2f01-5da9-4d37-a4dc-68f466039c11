<?php
// Set page variables
$page_title = 'My Cards';
$additional_css = ['dashboard.css'];
$additional_js = ['dashboard.js'];

// Include header template
require_once '../../templates/user/header.php';

// Include database connection
require_once '../../config/config.php';
requireLogin();

// Get user's virtual cards and comprehensive data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user's virtual cards with enhanced details
    $cards_sql = "SELECT vc.*,
                         COUNT(vct.id) as transaction_count,
                         SUM(CASE WHEN vct.transaction_type = 'debit' THEN vct.amount ELSE 0 END) as total_spent,
                         MAX(vct.created_at) as last_transaction_date
                  FROM virtual_cards vc
                  LEFT JOIN virtual_card_transactions vct ON vc.id = vct.card_id
                  WHERE vc.user_id = ?
                  GROUP BY vc.id
                  ORDER BY vc.created_at DESC";
    $cards_result = $db->query($cards_sql, [$user_id]);
    $virtual_cards = [];
    while ($card = $cards_result->fetch_assoc()) {
        $virtual_cards[] = $card;
    }

    // Get comprehensive card statistics
    $stats_sql = "SELECT
                    COUNT(*) as total_cards,
                    SUM(current_balance) as total_balance,
                    SUM(spending_limit) as total_limit,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cards,
                    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_cards,
                    AVG(spending_limit) as avg_limit
                  FROM virtual_cards WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $card_stats = $stats_result->fetch_assoc() ?: [
        'total_cards' => 0,
        'total_balance' => 0,
        'total_limit' => 0,
        'active_cards' => 0,
        'blocked_cards' => 0,
        'avg_limit' => 0
    ];

    // Get recent card transactions
    $recent_transactions_sql = "SELECT vct.*, vc.card_number, vc.card_type
                               FROM virtual_card_transactions vct
                               JOIN virtual_cards vc ON vct.card_id = vc.id
                               WHERE vc.user_id = ?
                               ORDER BY vct.created_at DESC
                               LIMIT 10";
    $recent_result = $db->query($recent_transactions_sql, [$user_id]);
    $recent_transactions = [];
    while ($transaction = $recent_result->fetch_assoc()) {
        $recent_transactions[] = $transaction;
    }

} catch (Exception $e) {
    error_log("Cards page error: " . $e->getMessage());
    $virtual_cards = [];
    $card_stats = [
        'total_cards' => 0,
        'total_balance' => 0,
        'total_limit' => 0,
        'active_cards' => 0,
        'blocked_cards' => 0,
        'avg_limit' => 0
    ];
    $recent_transactions = [];
}

// Card type configurations
$card_types = [
    'visa' => ['name' => 'Visa', 'color' => '#1a1f71', 'icon' => 'fab fa-cc-visa'],
    'mastercard' => ['name' => 'Mastercard', 'color' => '#eb001b', 'icon' => 'fab fa-cc-mastercard'],
    'amex' => ['name' => 'American Express', 'color' => '#006fcf', 'icon' => 'fab fa-cc-amex'],
    'discover' => ['name' => 'Discover', 'color' => '#ff6000', 'icon' => 'fab fa-cc-discover']
];
?>

<!-- Include Sidebar -->
<?php require_once '../../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">My Cards</h1>
                    <p class="text-muted">Manage your virtual cards and monitor spending</p>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#requestCardModal">
                    <i class="fas fa-plus me-2"></i>Request New Card
                </button>
            </div>
        </div>
    </div>

    <!-- Card Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-credit-card" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $card_stats['total_cards']; ?></h3>
                    <p class="text-muted mb-0">Total Cards</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-wallet" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1">$<?php echo number_format($card_stats['total_balance'], 2); ?></h3>
                    <p class="text-muted mb-0">Total Balance</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-chart-line" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1">$<?php echo number_format($card_stats['total_limit'], 2); ?></h3>
                    <p class="text-muted mb-0">Total Limit</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-shield-alt" style="font-size: 2rem;"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $card_stats['active_cards']; ?></h3>
                    <p class="text-muted mb-0">Active Cards</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Virtual Cards Display -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>Your Virtual Cards
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($virtual_cards)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-credit-card text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">No Cards Available</h5>
                            <p class="text-muted">Request your first virtual card to start making secure online payments</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#requestCardModal">
                                <i class="fas fa-plus me-2"></i>Request Card
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="row g-4">
                            <?php foreach ($virtual_cards as $card): ?>
                                <?php 
                                $card_type = $card_types[$card['card_type']] ?? $card_types['visa'];
                                $masked_number = '**** **** **** ' . substr($card['card_number'], -4);
                                ?>
                                <div class="col-md-6">
                                    <!-- Virtual Card Design -->
                                    <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, <?php echo $card_type['color']; ?> 0%, <?php echo $card_type['color']; ?>dd 100%); color: white; border-radius: 15px; height: 200px; position: relative; overflow: hidden;">
                                        <!-- Card Background Pattern -->
                                        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                                        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.05); border-radius: 50%;"></div>
                                        
                                        <div class="card-body d-flex flex-column justify-content-between" style="height: 100%; z-index: 2; position: relative;">
                                            <!-- Card Header -->
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1" style="color: rgba(255,255,255,0.9);"><?php echo htmlspecialchars($card['card_name']); ?></h6>
                                                    <small style="color: rgba(255,255,255,0.7);">
                                                        <?php echo ucfirst($card['status']); ?>
                                                    </small>
                                                </div>
                                                <i class="<?php echo $card_type['icon']; ?>" style="font-size: 2rem; color: rgba(255,255,255,0.9);"></i>
                                            </div>
                                            
                                            <!-- Card Number -->
                                            <div class="text-center">
                                                <h4 class="mb-0" style="letter-spacing: 2px; font-family: 'Courier New', monospace;">
                                                    <?php echo $masked_number; ?>
                                                </h4>
                                            </div>
                                            
                                            <!-- Card Footer -->
                                            <div class="d-flex justify-content-between align-items-end">
                                                <div>
                                                    <small style="color: rgba(255,255,255,0.7);">Balance</small>
                                                    <div class="fw-bold">$<?php echo number_format($card['current_balance'], 2); ?></div>
                                                </div>
                                                <div class="text-end">
                                                    <small style="color: rgba(255,255,255,0.7);">Expires</small>
                                                    <div class="fw-bold"><?php echo date('m/y', strtotime($card['expiry_date'])); ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Card Actions -->
                                    <div class="mt-3 d-flex gap-2">
                                        <button class="btn btn-sm btn-outline-primary flex-fill" onclick="viewCardDetails(<?php echo $card['id']; ?>)">
                                            <i class="fas fa-eye me-1"></i>Details
                                        </button>
                                        <button class="btn btn-sm btn-outline-success flex-fill" onclick="topUpCard(<?php echo $card['id']; ?>)">
                                            <i class="fas fa-plus me-1"></i>Top Up
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning flex-fill" onclick="freezeCard(<?php echo $card['id']; ?>)">
                                            <i class="fas fa-snowflake me-1"></i>
                                            <?php echo $card['status'] === 'active' ? 'Freeze' : 'Unfreeze'; ?>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Transactions
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_transactions)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-receipt text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No recent transactions</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($transaction['merchant_name']); ?></h6>
                                            <p class="mb-1 text-muted small">
                                                **** <?php echo substr($transaction['card_number'], -4); ?>
                                            </p>
                                            <small class="text-muted">
                                                <?php echo date('M j, g:i A', strtotime($transaction['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="text-danger">
                                                -$<?php echo number_format($transaction['amount'], 2); ?>
                                            </span>
                                            <br>
                                            <span class="badge bg-<?php echo $transaction['status'] === 'completed' ? 'success' : 'warning'; ?> badge-sm">
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#requestCardModal">
                            <i class="fas fa-plus me-2"></i>Request New Card
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-money-bill-wave me-2"></i>Top Up All Cards
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-download me-2"></i>Download Statements
                        </button>
                        <a href="<?php echo $base_url; ?>/dashboard/transactions/" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-2"></i>View All Transactions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewCardDetails(cardId) {
    alert('View details for card ID: ' + cardId);
}

function topUpCard(cardId) {
    alert('Top up card ID: ' + cardId);
}

function freezeCard(cardId) {
    alert('Freeze/Unfreeze card ID: ' + cardId);
}
</script>

<?php
// Include footer template
require_once '../../templates/user/footer.php';
?>
