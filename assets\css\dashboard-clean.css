/* ===== CLEAN DASHBOARD STYLES ===== */

/* CSS Variables */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: var(--background-light);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Dashboard Layout - Use existing structure */
.main-content {
    padding: 2rem;
    background-color: var(--background-light);
    min-height: 100vh;
}

/* Remove sidebar styles - using existing template */

/* Cards - NO THICK BORDERS */
.card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
}

.stats-icon.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stats-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stats-icon.primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.stats-value {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stats-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
}

.stats-change.positive {
    color: var(--success-color);
}

.stats-change.negative {
    color: var(--danger-color);
}

/* Account Balance Card */
.balance-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.balance-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--background-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--primary-color);
    text-decoration: none;
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

/* Recent Transactions */
.transaction-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.transaction-details {
    flex: 1;
}

.transaction-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.transaction-date {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.transaction-amount {
    font-weight: 600;
    font-size: 1rem;
}

.transaction-amount.credit {
    color: var(--success-color);
}

.transaction-amount.debit {
    color: var(--danger-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .banking-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .banking-sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
}

/* Utilities */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-light { background-color: var(--background-light) !important; }
.bg-white { background-color: var(--background-white) !important; }

.border-0 { border: none !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
