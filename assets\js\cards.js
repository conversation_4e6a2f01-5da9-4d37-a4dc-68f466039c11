// Virtual Cards Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize cards page functionality
    initializeCardsPage();
    
    // Initialize dropdown functionality
    initializeDropdowns();
    
    // Initialize modal functionality
    initializeModals();
});

// Cards page initialization
function initializeCardsPage() {
    console.log('Virtual Cards page initialized');
    
    // Add hover effects to virtual cards
    const virtualCards = document.querySelectorAll('.virtual-card');
    virtualCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-4px) scale(1)';
        });
    });
    
    // Add loading states to buttons
    const buttons = document.querySelectorAll('.btn-primary, .btn-outline');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.classList.contains('btn-loading')) {
                e.preventDefault();
                return;
            }
        });
    });
}

// Dropdown functionality
function initializeDropdowns() {
    const dropdownButtons = document.querySelectorAll('[data-bs-toggle="dropdown"]');
    
    dropdownButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                if (menu !== this.nextElementSibling) {
                    menu.classList.remove('show');
                }
            });
            
            // Toggle current dropdown
            const menu = this.nextElementSibling;
            if (menu && menu.classList.contains('dropdown-menu')) {
                menu.classList.toggle('show');
            }
        });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
        });
    });
}

// Modal functionality
function initializeModals() {
    // Close modal when clicking close button
    document.querySelectorAll('.btn-close, [data-bs-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                hideModal(modal);
            }
        });
    });
    
    // Close modal when clicking backdrop
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal(this);
            }
        });
    });
}

// Show modal
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

// Hide modal
function hideModal(modal) {
    if (typeof modal === 'string') {
        modal = document.getElementById(modal);
    }
    
    if (modal) {
        modal.classList.remove('show');
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

// Card management functions
function showCreateCardModal() {
    showModal('createCardModal');
}

function createCard() {
    const form = document.getElementById('createCardForm');
    const formData = new FormData(form);

    // Show loading state
    const createBtn = event.target;
    const originalText = createBtn.innerHTML;
    createBtn.innerHTML = '<span class="loading"></span> Creating...';
    createBtn.disabled = true;

    fetch('ajax/create_card.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Virtual card created successfully!', 'success');
            hideModal('createCardModal');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('Error: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while creating the card', 'danger');
    })
    .finally(() => {
        createBtn.innerHTML = originalText;
        createBtn.disabled = false;
    });
}

function viewCardDetails(cardId) {
    // Show loading state
    showAlert('Loading card details...', 'info');
    
    // Simulate API call
    setTimeout(() => {
        showAlert('Card details functionality coming soon!', 'info');
    }, 1000);
}

function topUpCard(cardId) {
    // Show loading state
    showAlert('Preparing top-up...', 'info');
    
    // Simulate API call
    setTimeout(() => {
        showAlert('Card top-up functionality coming soon!', 'info');
    }, 1000);
}

function freezeCard(cardId) {
    const confirmMessage = 'Are you sure you want to freeze this card? You can unfreeze it later.';
    
    if (confirm(confirmMessage)) {
        // Show loading state
        showAlert('Processing request...', 'info');
        
        // Simulate API call
        setTimeout(() => {
            showAlert('Card freeze functionality coming soon!', 'info');
        }, 1000);
    }
}

function deleteCard(cardId) {
    const confirmMessage = 'Are you sure you want to delete this card? This action cannot be undone.';
    
    if (confirm(confirmMessage)) {
        // Show loading state
        showAlert('Deleting card...', 'warning');
        
        // Simulate API call
        setTimeout(() => {
            showAlert('Card deletion functionality coming soon!', 'info');
        }, 1000);
    }
}

function showTopUpModal() {
    showAlert('Top-up modal coming soon!', 'info');
}

function downloadStatement() {
    // Show loading state
    showAlert('Preparing statement download...', 'info');
    
    // Simulate download
    setTimeout(() => {
        showAlert('Statement download functionality coming soon!', 'info');
    }, 1000);
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.alert-container') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.className = 'alert-container';
    container.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1002;
        max-width: 400px;
    `;
    
    document.body.appendChild(container);
    return container;
}

// Loading state management
function setLoading(element, loading = true) {
    if (loading) {
        element.classList.add('btn-loading');
        element.disabled = true;
        const originalText = element.innerHTML;
        element.dataset.originalText = originalText;
        element.innerHTML = '<span class="loading"></span> Loading...';
    } else {
        element.classList.remove('btn-loading');
        element.disabled = false;
        element.innerHTML = element.dataset.originalText || element.innerHTML;
    }
}

// Card number formatting
function formatCardNumber(input) {
    // Remove all non-digit characters
    let value = input.value.replace(/\D/g, '');
    
    // Add spaces every 4 digits
    value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
    
    // Limit to 19 characters (16 digits + 3 spaces)
    if (value.length > 19) {
        value = value.substring(0, 19);
    }
    
    input.value = value;
}

// Export functions for global use
window.CardsPage = {
    showCreateCardModal,
    createCard,
    viewCardDetails,
    topUpCard,
    freezeCard,
    deleteCard,
    showTopUpModal,
    downloadStatement,
    showAlert,
    setLoading,
    formatCardNumber
};
