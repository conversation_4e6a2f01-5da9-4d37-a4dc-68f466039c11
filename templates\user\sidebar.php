<?php
// Get current page for active navigation
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Get the base URL for proper linking
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . '://' . $host . '/online_banking';
?>

<!-- Banking Sidebar Navigation -->
<div class="banking-sidebar">
    <!-- Bank Logo & Brand -->
    <div class="sidebar-header">
        <div class="bank-logo">
            <div class="logo-icon">
                <svg width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                </svg>
            </div>
            <div class="bank-name">
                <?php echo htmlspecialchars($site_name ?? 'PremierBank Pro'); ?>
            </div>
        </div>
        <div class="user-info">
            <div class="user-avatar">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo htmlspecialchars(($_SESSION['first_name'] ?? '') . ' ' . ($_SESSION['last_name'] ?? '')); ?></div>
                <div class="account-number">•••• <?php echo substr($_SESSION['account_number'] ?? '', -4); ?></div>
            </div>
        </div>
    </div>

    <!-- Main Banking Navigation -->
    <nav class="sidebar-nav">
        <!-- Overview Section -->
        <div class="nav-section">
            <div class="nav-section-title">Overview</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/" class="nav-link <?php echo $current_page === 'index' && $current_dir === 'dashboard' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/accounts/" class="nav-link <?php echo $current_dir === 'accounts' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z"/>
                        </svg>
                        <span>My Accounts</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Banking Services Section -->
        <div class="nav-section">
            <div class="nav-section-title">Banking Services</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/transfers/" class="nav-link <?php echo $current_dir === 'transfers' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                        </svg>
                        <span>Transfer Money</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/payments/" class="nav-link <?php echo $current_dir === 'payments' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                        </svg>
                        <span>Bill Payments</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/transactions/" class="nav-link <?php echo $current_dir === 'transactions' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <span>Transaction History</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/beneficiaries/" class="nav-link <?php echo $current_dir === 'beneficiaries' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                        <span>Beneficiaries</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Cards & Digital Section -->
        <div class="nav-section">
            <div class="nav-section-title">Cards & Digital</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/cards/" class="nav-link <?php echo $current_dir === 'cards' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>My Cards</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/statements/" class="nav-link <?php echo $current_dir === 'statements' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                        </svg>
                        <span>Statements</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Analytics & Reports Section -->
        <div class="nav-section">
            <div class="nav-section-title">Analytics</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/analytics/" class="nav-link <?php echo $current_dir === 'analytics' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                        </svg>
                        <span>Spending Insights</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/reports/" class="nav-link <?php echo $current_dir === 'reports' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"/>
                        </svg>
                        <span>Reports</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Support & Settings Section -->
        <div class="nav-section">
            <div class="nav-section-title">Support</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/dashboard/security/" class="nav-link <?php echo $current_dir === 'security' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Security Center</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/support/" class="nav-link <?php echo $current_dir === 'support' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                        </svg>
                        <span>Help & Support</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/profile/" class="nav-link <?php echo $current_dir === 'profile' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                        </svg>
                        <span>Profile & Settings</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Logout Section -->
    <div class="sidebar-footer">
        <a href="<?php echo $base_url; ?>/auth/logout.php" class="logout-link">
            <svg class="logout-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 01-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
            </svg>
            <span>Sign Out</span>
        </a>
    </div>
</div>
