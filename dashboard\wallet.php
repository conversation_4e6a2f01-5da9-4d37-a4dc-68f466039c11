<?php
// Set page variables
$page_title = 'Wallet Details';
$additional_css = ['dashboard-clean.css'];

// Include header template
require_once '../templates/user/header.php';

// Include database connection and check authentication
require_once '../config/config.php';
requireLogin();

// Get comprehensive user wallet data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user account information
    $user_sql = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    // Get virtual cards
    $cards_sql = "SELECT * FROM virtual_cards WHERE user_id = ? ORDER BY created_at DESC";
    $cards_result = $db->query($cards_sql, [$user_id]);
    $virtual_cards = [];
    while ($card = $cards_result->fetch_assoc()) {
        $virtual_cards[] = $card;
    }

    // Get transaction statistics
    $stats_sql = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as total_credits,
                    SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as total_debits,
                    AVG(CASE WHEN transaction_type = 'credit' THEN amount ELSE NULL END) as avg_credit,
                    AVG(CASE WHEN transaction_type = 'debit' THEN amount ELSE NULL END) as avg_debit,
                    MAX(created_at) as last_transaction_date
                  FROM account_transactions 
                  WHERE account_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $transaction_stats = $stats_result->fetch_assoc();

    // Get transfer statistics
    $transfer_stats_sql = "SELECT 
                            COUNT(*) as total_transfers,
                            SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                            SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received,
                            COUNT(CASE WHEN sender_id = ? THEN 1 END) as transfers_sent,
                            COUNT(CASE WHEN recipient_id = ? THEN 1 END) as transfers_received
                           FROM transfers 
                           WHERE (sender_id = ? OR recipient_id = ?) AND status = 'completed'";
    $transfer_stats_result = $db->query($transfer_stats_sql, [$user_id, $user_id, $user_id, $user_id, $user_id, $user_id]);
    $transfer_stats = $transfer_stats_result->fetch_assoc();

    // Get recent activity (last 10 transactions)
    $recent_activity_sql = "SELECT 'admin_transaction' as type, transaction_type, amount, description, created_at, reference_number
                           FROM account_transactions 
                           WHERE account_id = ?
                           UNION ALL
                           SELECT 'transfer' as type, 
                                  CASE WHEN sender_id = ? THEN 'debit' ELSE 'credit' END as transaction_type,
                                  amount, 
                                  CONCAT('Transfer ', CASE WHEN sender_id = ? THEN 'to ' ELSE 'from ' END, 
                                         COALESCE(recipient_name, recipient_account)) as description,
                                  created_at,
                                  transaction_id as reference_number
                           FROM transfers 
                           WHERE (sender_id = ? OR recipient_id = ?) AND status = 'completed'
                           ORDER BY created_at DESC 
                           LIMIT 10";
    $recent_activity_result = $db->query($recent_activity_sql, [$user_id, $user_id, $user_id, $user_id, $user_id]);
    $recent_activity = [];
    while ($row = $recent_activity_result->fetch_assoc()) {
        $recent_activity[] = $row;
    }

} catch (Exception $e) {
    error_log("Wallet details error: " . $e->getMessage());
    $user = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'account_number' => '', 'balance' => 0];
    $virtual_cards = [];
    $transaction_stats = ['total_transactions' => 0, 'total_credits' => 0, 'total_debits' => 0, 'avg_credit' => 0, 'avg_debit' => 0];
    $transfer_stats = ['total_transfers' => 0, 'total_sent' => 0, 'total_received' => 0, 'transfers_sent' => 0, 'transfers_received' => 0];
    $recent_activity = [];
}

?>

<!-- Include Sidebar -->
<?php require_once '../templates/user/sidebar.php'; ?>

<!-- Main Content -->
<div class="main-content">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">Wallet Details</h1>
                    <p class="text-muted">Comprehensive overview of your financial account</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="statements/" class="btn btn-outline-primary">
                        <i class="fas fa-file-alt me-2"></i>View Statements
                    </a>
                    <a href="transfers/" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Make Transfer
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="wallet-overview-card">
                <div class="wallet-header">
                    <div class="account-info">
                        <h2><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h2>
                        <p class="account-details">
                            Account: <?php echo htmlspecialchars($user['account_number']); ?> | 
                            Type: <?php echo ucfirst($user['account_type']); ?> | 
                            Status: <span class="status-<?php echo $user['status']; ?>"><?php echo ucfirst($user['status']); ?></span>
                        </p>
                    </div>
                    <div class="balance-section">
                        <div class="balance-label">Current Balance</div>
                        <div class="balance-amount">$<?php echo number_format($user['balance'], 2); ?></div>
                        <div class="balance-currency"><?php echo $user['currency'] ?? 'USD'; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="stats-value"><?php echo number_format($transaction_stats['total_transactions'] ?? 0); ?></div>
                <div class="stats-label">Total Transactions</div>
                <div class="stats-detail">Admin-created transactions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="stats-value">$<?php echo number_format($transaction_stats['total_credits'] ?? 0, 2); ?></div>
                <div class="stats-label">Total Credits</div>
                <div class="stats-detail">Money added to account</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="stats-value">$<?php echo number_format($transaction_stats['total_debits'] ?? 0, 2); ?></div>
                <div class="stats-label">Total Debits</div>
                <div class="stats-detail">Money deducted from account</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="stats-value"><?php echo number_format($transfer_stats['total_transfers'] ?? 0); ?></div>
                <div class="stats-label">Total Transfers</div>
                <div class="stats-detail">User-initiated transfers</div>
            </div>
        </div>
    </div>

    <!-- Detailed Information Grid -->
    <div class="row">
        <!-- Account Details -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="detail-row">
                        <span class="label">Full Name</span>
                        <span class="value"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Email</span>
                        <span class="value"><?php echo htmlspecialchars($user['email']); ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Phone</span>
                        <span class="value"><?php echo htmlspecialchars($user['phone'] ?? 'Not provided'); ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Account Type</span>
                        <span class="value"><?php echo ucfirst($user['account_type']); ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Currency</span>
                        <span class="value"><?php echo $user['currency'] ?? 'USD'; ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Member Since</span>
                        <span class="value"><?php echo date('M j, Y', strtotime($user['created_at'])); ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">KYC Status</span>
                        <span class="value status-<?php echo $user['kyc_status']; ?>"><?php echo ucfirst($user['kyc_status']); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Virtual Cards -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i>Virtual Cards
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($virtual_cards)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-credit-card text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-3">No virtual cards</p>
                            <a href="cards/" class="btn btn-primary btn-sm">Request Card</a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($virtual_cards as $card): ?>
                            <div class="card-summary-item">
                                <div class="card-info">
                                    <div class="card-name"><?php echo htmlspecialchars($card['card_holder_name']); ?></div>
                                    <div class="card-number">**** **** **** <?php echo substr($card['card_number'], -4); ?></div>
                                    <div class="card-details">
                                        <span class="card-type"><?php echo ucfirst($card['card_type']); ?></span>
                                        <span class="card-status status-<?php echo $card['status']; ?>"><?php echo ucfirst($card['status']); ?></span>
                                    </div>
                                </div>
                                <div class="card-balance">
                                    <div class="balance">$<?php echo number_format($card['current_balance'], 2); ?></div>
                                    <div class="limit">Limit: $<?php echo number_format($card['spending_limit'], 2); ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center mt-3">
                            <a href="cards/" class="btn btn-outline-primary btn-sm">Manage Cards</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_activity)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-clock text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No recent activity</p>
                        </div>
                    <?php else: ?>
                        <div class="activity-list">
                            <?php foreach (array_slice($recent_activity, 0, 8) as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon <?php echo $activity['transaction_type'] === 'credit' ? 'success' : 'warning'; ?>">
                                        <i class="fas fa-<?php echo $activity['transaction_type'] === 'credit' ? 'arrow-down' : 'arrow-up'; ?>"></i>
                                    </div>
                                    <div class="activity-details">
                                        <div class="activity-title"><?php echo htmlspecialchars($activity['description']); ?></div>
                                        <div class="activity-date"><?php echo date('M j, g:i A', strtotime($activity['created_at'])); ?></div>
                                        <div class="activity-type">
                                            <span class="badge bg-<?php echo $activity['type'] === 'admin_transaction' ? 'info' : 'secondary'; ?> badge-sm">
                                                <?php echo $activity['type'] === 'admin_transaction' ? 'Admin' : 'Transfer'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="activity-amount">
                                        <span class="amount <?php echo $activity['transaction_type'] === 'credit' ? 'positive' : 'negative'; ?>">
                                            <?php echo $activity['transaction_type'] === 'credit' ? '+' : '-'; ?>$<?php echo number_format($activity['amount'], 2); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-3">
                            <a href="transactions/" class="btn btn-outline-primary btn-sm">View All Activity</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
require_once '../templates/user/footer.php';
?>
