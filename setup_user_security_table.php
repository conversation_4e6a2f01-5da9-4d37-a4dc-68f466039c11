<?php
/**
 * Setup script for user security settings table
 */

require_once 'config/config.php';

echo "<h2>Setting up User Security Settings Table</h2>";

try {
    $db = getDB();
    $sql = file_get_contents('database/create_user_security_settings.sql');
    
    // Split by semicolon but be careful with the split
    $statements = preg_split('/;\s*$/m', $sql);
    
    foreach ($statements as $stmt) {
        $stmt = trim($stmt);
        if (!empty($stmt)) {
            try {
                $db->getConnection()->query($stmt);
                echo "<p style='color: green;'>✓ Executed: " . substr(str_replace(["\n", "\r"], ' ', $stmt), 0, 70) . "...</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
                echo "<p style='color: orange;'>Statement: " . substr($stmt, 0, 100) . "...</p>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>✓ User security settings table setup complete!</h3>";
    
    // Check if table was created successfully
    $check_query = "SELECT COUNT(*) as count FROM user_security_settings";
    $result = $db->query($check_query);
    $count = $result->fetch_assoc()['count'];
    
    echo "<p>Total user security settings records: <strong>$count</strong></p>";
    
    // Show sample data
    $sample_query = "SELECT uss.*, a.username, a.first_name, a.last_name 
                     FROM user_security_settings uss 
                     JOIN accounts a ON uss.user_id = a.id 
                     LIMIT 5";
    $sample_result = $db->query($sample_query);
    
    if ($sample_result && $sample_result->num_rows > 0) {
        echo "<h4>Sample Security Settings:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>User</th><th>OTP Enabled</th><th>2FA Required</th><th>Login Attempts Limit</th><th>Lockout Duration</th></tr>";
        
        while ($row = $sample_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['username'] . " (" . $row['first_name'] . " " . $row['last_name'] . ")") . "</td>";
            echo "<td>" . ($row['otp_enabled'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . ($row['require_2fa'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . $row['login_attempts_limit'] . "</td>";
            echo "<td>" . $row['lockout_duration'] . " minutes</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Fatal error: " . $e->getMessage() . "</p>";
}

echo "<br><a href='admin/user-security-management.php'>Go to User Security Management</a>";
echo "<br><a href='login.php'>Go to Login Page</a>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
