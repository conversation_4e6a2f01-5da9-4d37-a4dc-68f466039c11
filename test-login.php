<?php
/**
 * Test Login Page - Debug Version
 * This is a test version of the login page with detailed debugging
 */

require_once 'config/config.php';
require_once 'includes/user-2fa-functions.php';

// Start output buffering to capture any errors
ob_start();

$debug_info = [];
$login_attempted = false;
$login_success = false;
$redirect_url = '';
$error_message = '';

// Add debug function
function addDebug($step, $message, $data = null) {
    global $debug_info;
    $debug_info[] = [
        'step' => $step,
        'message' => $message,
        'data' => $data,
        'time' => microtime(true)
    ];
}

addDebug('INIT', 'Test login page loaded');

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $login_attempted = true;
    addDebug('LOGIN_START', 'Login form submitted');
    
    try {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        addDebug('INPUT', 'Credentials received', [
            'username' => $username,
            'password_length' => strlen($password)
        ]);
        
        if (empty($username) || empty($password)) {
            throw new Exception('Username and password are required');
        }
        
        // Database connection
        $db = getDB();
        addDebug('DB', 'Database connection established');
        
        // Find user by username, email, or account number
        $sql = "SELECT id, username, first_name, last_name, email, password, account_number,
                       balance, status, is_admin, kyc_status, created_at
                FROM accounts
                WHERE username = ? OR email = ? OR account_number = ?";
        
        addDebug('QUERY', 'Searching for user', ['sql' => $sql, 'search_term' => $username]);
        
        $result = $db->query($sql, [$username, $username, $username]);
        
        if (!$result || $result->num_rows === 0) {
            addDebug('USER_NOT_FOUND', 'No user found with provided credentials');
            throw new Exception('Invalid login credentials');
        }
        
        $user = $result->fetch_assoc();
        addDebug('USER_FOUND', 'User found in database', [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'is_admin' => $user['is_admin'],
            'status' => $user['status']
        ]);
        
        // Verify password
        if (!verifyPassword($password, $user['password'])) {
            addDebug('PASSWORD_FAIL', 'Password verification failed');
            throw new Exception('Invalid login credentials');
        }
        
        addDebug('PASSWORD_OK', 'Password verification successful');
        
        // Check account status (same logic as original login)
        if ($user['status'] === 'pending') {
            addDebug('ACCOUNT_PENDING', 'Account is pending approval', ['status' => $user['status']]);
            throw new Exception('Your account is pending admin approval. Please wait for activation.');
        } elseif ($user['status'] === 'suspended') {
            addDebug('ACCOUNT_SUSPENDED', 'Account is suspended', ['status' => $user['status']]);
            throw new Exception('Your account has been suspended. Please contact support.');
        } elseif ($user['status'] === 'closed') {
            addDebug('ACCOUNT_CLOSED', 'Account is closed', ['status' => $user['status']]);
            throw new Exception('Your account has been closed. Please contact support.');
        }
        
        addDebug('ACCOUNT_ACTIVE', 'Account status is valid', ['status' => $user['status']]);
        
        // Check if this is a super admin
        if ($user['is_admin'] == 1) {
            addDebug('ADMIN_USER', 'User is admin - should use admin login');
            throw new Exception('Admin users should use the admin login portal');
        }
        
        addDebug('REGULAR_USER', 'User is regular user - proceeding with user login');
        
        // Get user security settings
        $security_settings = getUserSecuritySettings($user['id']);
        addDebug('SECURITY_SETTINGS', 'Retrieved security settings', $security_settings);
        
        // Check if OTP verification is needed
        $should_verify_otp = shouldUserVerifyOTP($user['id']);
        addDebug('OTP_CHECK', 'OTP verification decision', [
            'should_verify_otp' => $should_verify_otp,
            'otp_enabled' => $security_settings['otp_enabled'] ?? 'unknown',
            'require_2fa' => $security_settings['require_2fa'] ?? 'unknown',
            'google_2fa_enabled' => $security_settings['google_2fa_enabled'] ?? 'unknown'
        ]);
        
        if ($should_verify_otp) {
            addDebug('REDIRECT_OTP', 'User should go to OTP verification');
            
            // Set session for OTP verification
            $_SESSION['otp_user_id'] = $user['id'];
            $_SESSION['otp_pending'] = true;
            
            // Generate and send OTP (simplified for testing)
            $otp_code = sprintf('%06d', mt_rand(0, 999999));
            $_SESSION['otp_code'] = $otp_code;
            $_SESSION['otp_expires'] = time() + (10 * 60); // 10 minutes
            
            addDebug('OTP_GENERATED', 'OTP code generated', [
                'otp_code' => $otp_code,
                'expires_at' => date('Y-m-d H:i:s', $_SESSION['otp_expires'])
            ]);
            
            $redirect_url = 'auth/verify-otp.php';
            
        } else {
            addDebug('DIRECT_LOGIN', 'User should login directly to dashboard');
            
            // Set session variables for direct login
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['first_name'] = $user['first_name'];
            $_SESSION['last_name'] = $user['last_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['account_number'] = $user['account_number'];
            $_SESSION['balance'] = $user['balance'];
            $_SESSION['is_admin'] = (bool)$user['is_admin'];
            $_SESSION['kyc_status'] = $user['kyc_status'];
            $_SESSION['last_activity'] = time();
            $_SESSION['user_logged_in'] = true;
            $_SESSION['login_time'] = time();
            
            addDebug('SESSION_SET', 'Session variables set for direct login', [
                'user_id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'is_admin' => $_SESSION['is_admin'],
                'user_logged_in' => $_SESSION['user_logged_in']
            ]);
            
            // DO NOT set is_admin_session for regular users
            if (isset($_SESSION['is_admin_session'])) {
                unset($_SESSION['is_admin_session']);
                addDebug('ADMIN_SESSION_REMOVED', 'Removed is_admin_session flag');
            }
            
            // Test isLoggedIn() function
            $is_logged_in_test = isLoggedIn();
            addDebug('LOGIN_TEST', 'Testing isLoggedIn() function', [
                'result' => $is_logged_in_test,
                'session_user_id' => $_SESSION['user_id'] ?? 'not_set',
                'session_username' => $_SESSION['username'] ?? 'not_set',
                'session_is_admin_session' => $_SESSION['is_admin_session'] ?? 'not_set'
            ]);
            
            // Log activity
            if (function_exists('logActivity')) {
                logActivity($user['id'], 'User logged in successfully (2FA/OTP not required)');
                addDebug('ACTIVITY_LOGGED', 'Login activity logged');
            }
            
            $redirect_url = 'dashboard/';
        }
        
        $login_success = true;
        addDebug('LOGIN_SUCCESS', 'Login process completed successfully', [
            'redirect_url' => $redirect_url
        ]);
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        addDebug('LOGIN_ERROR', 'Login failed with exception', [
            'error' => $error_message,
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
    }
}

// Get any output buffer content
$output_buffer = ob_get_clean();
if (!empty($output_buffer)) {
    addDebug('OUTPUT_BUFFER', 'Captured output buffer content', ['content' => $output_buffer]);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login - Debug Version</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .debug-step {
            margin-bottom: 10px;
            padding: 8px;
            border-left: 4px solid #007bff;
            background-color: white;
        }
        .debug-step.error {
            border-left-color: #dc3545;
            background-color: #fff5f5;
        }
        .debug-step.success {
            border-left-color: #28a745;
            background-color: #f8fff8;
        }
        .debug-data {
            font-family: monospace;
            font-size: 12px;
            background-color: #f1f3f4;
            padding: 5px;
            border-radius: 3px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-bug"></i> Test Login - Debug Version</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($login_attempted): ?>
                            <?php if ($login_success): ?>
                                <div class="alert alert-success">
                                    <h5><i class="fas fa-check-circle"></i> Login Successful!</h5>
                                    <p><strong>Redirect URL:</strong> <?php echo htmlspecialchars($redirect_url); ?></p>
                                    <div class="mt-3">
                                        <a href="<?php echo htmlspecialchars($redirect_url); ?>" class="btn btn-success">
                                            <i class="fas fa-arrow-right"></i> Continue to <?php echo $redirect_url; ?>
                                        </a>
                                        <button type="button" class="btn btn-info" onclick="location.reload()">
                                            <i class="fas fa-redo"></i> Test Again
                                        </button>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h5><i class="fas fa-exclamation-triangle"></i> Login Failed</h5>
                                    <p><?php echo htmlspecialchars($error_message); ?></p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username, Email, or Account Number</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" name="login" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> Test Login
                            </button>
                        </form>

                        <div class="mt-4">
                            <h6>Quick Test Credentials:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <button type="button" class="btn btn-sm btn-outline-secondary w-100" 
                                            onclick="fillCredentials('<EMAIL>', 'loving12')">
                                        Email Login
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-sm btn-outline-secondary w-100" 
                                            onclick="fillCredentials('<EMAIL>', 'loving12')">
                                        Username Login
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="debug-panel">
                    <h5><i class="fas fa-terminal"></i> Debug Information</h5>
                    
                    <?php if (!empty($debug_info)): ?>
                        <div style="max-height: 600px; overflow-y: auto;">
                            <?php foreach ($debug_info as $index => $debug): ?>
                                <div class="debug-step <?php echo strpos($debug['step'], 'ERROR') !== false ? 'error' : (strpos($debug['step'], 'SUCCESS') !== false ? 'success' : ''); ?>">
                                    <strong><?php echo $index + 1; ?>. <?php echo htmlspecialchars($debug['step']); ?>:</strong>
                                    <?php echo htmlspecialchars($debug['message']); ?>
                                    <?php if ($debug['data']): ?>
                                        <div class="debug-data">
                                            <?php echo htmlspecialchars(json_encode($debug['data'], JSON_PRETTY_PRINT)); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No debug information yet. Submit the login form to see debug details.</p>
                    <?php endif; ?>
                </div>

                <div class="mt-3">
                    <h6>Current Session State:</h6>
                    <div class="debug-data">
                        <?php 
                        $session_data = [];
                        foreach ($_SESSION as $key => $value) {
                            if (is_bool($value)) {
                                $session_data[$key] = $value ? 'true' : 'false';
                            } else {
                                $session_data[$key] = $value;
                            }
                        }
                        echo htmlspecialchars(json_encode($session_data, JSON_PRETTY_PRINT)); 
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="clear_session.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-trash"></i> Clear Session
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="dashboard/" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-tachometer-alt"></i> Try Dashboard
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="admin/user-security-management.php" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-cog"></i> Security Settings
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="final_login_test.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-check"></i> Logic Tests
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }
    </script>
</body>
</html>
